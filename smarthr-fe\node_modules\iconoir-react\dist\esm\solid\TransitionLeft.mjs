"use client";var i=Object.defineProperty;var n=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var C=(r,e,o)=>e in r?i(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,t=(r,e)=>{for(var o in e||(e={}))f.call(e,o)&&C(r,o,e[o]);if(n)for(var o of n(e))p.call(e,o)&&C(r,o,e[o]);return r};import*as l from"react";import{forwardRef as s}from"react";import{IconoirContext as m}from"../IconoirContext.mjs";const u=(r,e)=>{const o=l.useContext(m),d=t(t({},o),r);return l.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:e},d),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.25 6C13.25 3.92894 14.9289 2.25 17 2.25H19C21.0711 2.25 22.75 3.92894 22.75 6V18C22.75 20.0711 21.0711 21.75 19 21.75H17C14.9289 21.75 13.25 20.0711 13.25 18V6Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.25 7C1.25 4.37665 3.37665 2.25 6 2.25H8C8.41421 2.25 8.75 2.58579 8.75 3C8.75 3.41421 8.41421 3.75 8 3.75H6C4.20507 3.75 2.75 5.20507 2.75 7V17C2.75 18.7949 4.20508 20.25 6 20.25H8C8.41421 20.25 8.75 20.5858 8.75 21C8.75 21.4142 8.41421 21.75 8 21.75H6C3.37664 21.75 1.25 19.6233 1.25 17V7Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.53033 8.46967C9.82322 8.76256 9.82322 9.23744 9.53033 9.53033L7.81066 11.25H14C14.4142 11.25 14.75 11.5858 14.75 12C14.75 12.4142 14.4142 12.75 14 12.75H7.81066L9.53033 14.4697C9.82322 14.7626 9.82322 15.2374 9.53033 15.5303C9.23744 15.8232 8.76256 15.8232 8.46967 15.5303L5.46967 12.5303C5.17678 12.2374 5.17678 11.7626 5.46967 11.4697L8.46967 8.46967C8.76256 8.17678 9.23744 8.17678 9.53033 8.46967Z",fill:"currentColor"}))},c=s(u);var H=c;export{H as default};
