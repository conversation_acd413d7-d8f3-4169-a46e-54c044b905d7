"use client";var m=Object.defineProperty;var l=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;var C=(r,o,e)=>o in r?m(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))p.call(o,e)&&C(r,e,o[e]);if(l)for(var e of l(o))s.call(o,e)&&C(r,e,o[e]);return r};import*as n from"react";import{forwardRef as i}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const V=(r,o)=>{const e=n.useContext(c),f=t(t({},e),r);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},f),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 3.6C2.25 2.85441 2.85442 2.25 3.6 2.25H20.4C21.1456 2.25 21.75 2.85442 21.75 3.6V20.4C21.75 21.1456 21.1456 21.75 20.4 21.75H3.6C2.85441 21.75 2.25 21.1456 2.25 20.4V3.6ZM12 6.25C12.4142 6.25 12.75 6.58579 12.75 7V13C12.75 13.4142 12.4142 13.75 12 13.75C11.5858 13.75 11.25 13.4142 11.25 13V7C11.25 6.58579 11.5858 6.25 12 6.25ZM12.5675 17.5008C12.8446 17.1929 12.8196 16.7187 12.5117 16.4416C12.2038 16.1645 11.7296 16.1894 11.4525 16.4973L11.4425 16.5084C11.1654 16.8163 11.1904 17.2905 11.4983 17.5676C11.8062 17.8447 12.2804 17.8197 12.5575 17.5119L12.5675 17.5008Z",fill:"currentColor"}))},d=i(V);var g=d;export{g as default};
