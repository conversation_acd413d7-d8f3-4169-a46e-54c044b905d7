import os
import logging

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Importar configuración de Azure Monitor para OpenTelemetry
# from azure.monitor.opentelemetry import configure_azure_monitor

from opentelemetry import trace
from opentelemetry._logs import set_logger_provider
from opentelemetry.sdk._logs import (
    <PERSON><PERSON><PERSON><PERSON>ider,
    LoggingHandler,
)
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.sdk.trace import TracerProvider

from azure.monitor.opentelemetry.exporter import AzureMonitorLogExporter
trace.set_tracer_provider(TracerProvider())
tracer = trace.get_tracer(__name__)
logger_provider = LoggerProvider()
set_logger_provider(logger_provider)

# Optional Azure Monitor setup
connection_string = os.environ.get("APPLICATIONINSIGHTS_CONNECTION_STRING")
if connection_string and connection_string.strip():
    try:
        exporter = AzureMonitorLogExporter(connection_string=connection_string)
        logger_provider.add_log_record_processor(BatchLogRecordProcessor(exporter))
        print("Azure Monitor logging enabled")
    except Exception as e:
        print(f"Warning: Could not initialize Azure Monitor: {e}")
        print("Continuing without Azure Monitor logging...")
else:
    print("Azure Monitor connection string not provided, skipping telemetry setup")

# Configurar el logger de Python (los logs se enviarán a App Insights por OpenTelemetry)
handler = LoggingHandler()
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

app = FastAPI(title="Semantic Matching Service")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
)


# Importar tu router después de configurar la instrumentación
from routes.routes import router as api_router
from routes.routes_interview import router as api_router_interview
from routes.routes_candidate import router as api_router_candidate
from routes.routes_position import router as api_router_position
from routes.routes_project import router as api_router_project
from routes.routes_note import router as api_router_note
from routes.routes_websocket import router as api_router_websocket
from routes.routes_linkedin import router as api_router_external_source
from routes.routes_recruiter import router as api_router_recruiter

app.include_router(api_router)
app.include_router(api_router_candidate, prefix="/candidate", tags=["candidate"])
app.include_router(api_router_interview, prefix="/interview", tags=["interview"])
app.include_router(api_router_position, prefix="/position", tags=["position"])
app.include_router(api_router_project, prefix="/project", tags=["project"])
app.include_router(api_router_note, prefix="/note", tags=["note"])
app.include_router(api_router_websocket, prefix="/ws", tags=["websocket"])
app.include_router(api_router_external_source, prefix="/api", tags=["LinkedIn Integration"])
app.include_router(api_router_recruiter, prefix="/recruiter", tags=["recruiter"])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
