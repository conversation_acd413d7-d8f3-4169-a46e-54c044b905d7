export {IconoirContext,IconoirProvider} from "./IconoirContext.mjs";import Icon from "./icon.mjs";type I = typeof Icon;export declare const AccessibilitySign: I;export declare const AccessibilityTech: I;export declare const Accessibility: I;export declare const Activity: I;export declare const AdobeAfterEffects: I;export declare const AdobeIllustrator: I;export declare const AdobeIndesign: I;export declare const AdobeLightroom: I;export declare const AdobePhotoshop: I;export declare const AdobeXd: I;export declare const AfricanTree: I;export declare const Agile: I;export declare const AirConditioner: I;export declare const AirplaneHelix45deg: I;export declare const AirplaneHelix: I;export declare const AirplaneOff: I;export declare const AirplaneRotation: I;export declare const Airplane: I;export declare const Airplay: I;export declare const Alarm: I;export declare const AlbumCarousel: I;export declare const AlbumList: I;export declare const AlbumOpen: I;export declare const Album: I;export declare const AlignBottomBox: I;export declare const AlignCenter: I;export declare const AlignHorizontalCenters: I;export declare const AlignHorizontalSpacing: I;export declare const AlignJustify: I;export declare const AlignLeftBox: I;export declare const AlignLeft: I;export declare const AlignRightBox: I;export declare const AlignRight: I;export declare const AlignTopBox: I;export declare const AlignVerticalCenters: I;export declare const AlignVerticalSpacing: I;export declare const AngleTool: I;export declare const AntennaOff: I;export declare const AntennaSignalTag: I;export declare const AntennaSignal: I;export declare const Antenna: I;export declare const AppNotification: I;export declare const AppStore: I;export declare const AppWindow: I;export declare const AppleHalf: I;export declare const AppleImac2021Side: I;export declare const AppleImac2021: I;export declare const AppleMac: I;export declare const AppleShortcuts: I;export declare const AppleSwift: I;export declare const AppleWallet: I;export declare const Apple: I;export declare const ArTag: I;export declare const Arc3dCenterPoint: I;export declare const Arc3d: I;export declare const Arcade: I;export declare const ArcheryMatch: I;export declare const Archery: I;export declare const Archive: I;export declare const AreaSearch: I;export declare const ArrowArchery: I;export declare const ArrowDownCircle: I;export declare const ArrowDownLeftCircle: I;export declare const ArrowDownLeftSquare: I;export declare const ArrowDownLeft: I;export declare const ArrowDownRightCircle: I;export declare const ArrowDownRightSquare: I;export declare const ArrowDownRight: I;export declare const ArrowDownTag: I;export declare const ArrowDown: I;export declare const ArrowEmailForward: I;export declare const ArrowEnlargeTag: I;export declare const ArrowLeftCircle: I;export declare const ArrowLeftTag: I;export declare const ArrowLeft: I;export declare const ArrowReduceTag: I;export declare const ArrowRightCircle: I;export declare const ArrowRightTag: I;export declare const ArrowRight: I;export declare const ArrowSeparateVertical: I;export declare const ArrowSeparate: I;export declare const ArrowUnionVertical: I;export declare const ArrowUnion: I;export declare const ArrowUpCircle: I;export declare const ArrowUpLeftCircle: I;export declare const ArrowUpLeftSquare: I;export declare const ArrowUpLeft: I;export declare const ArrowUpRightCircle: I;export declare const ArrowUpRightSquare: I;export declare const ArrowUpRight: I;export declare const ArrowUpTag: I;export declare const ArrowUp: I;export declare const ArrowsUpFromLine: I;export declare const Asana: I;export declare const Asterisk: I;export declare const AtSignCircle: I;export declare const AtSign: I;export declare const Atom: I;export declare const Attachment: I;export declare const AugmentedReality: I;export declare const AutoFlash: I;export declare const AviFormat: I;export declare const Axes: I;export declare const Backward15Seconds: I;export declare const BadgeCheck: I;export declare const Bag: I;export declare const Balcony: I;export declare const Bank: I;export declare const Barcode: I;export declare const BasketballField: I;export declare const Basketball: I;export declare const Bathroom: I;export declare const Battery25: I;export declare const Battery50: I;export declare const Battery75: I;export declare const BatteryCharging: I;export declare const BatteryEmpty: I;export declare const BatteryFull: I;export declare const BatteryIndicator: I;export declare const BatterySlash: I;export declare const BatteryWarning: I;export declare const Bbq: I;export declare const BeachBag: I;export declare const BedReady: I;export declare const Bed: I;export declare const BehanceTag: I;export declare const Behance: I;export declare const BellNotification: I;export declare const BellOff: I;export declare const Bell: I;export declare const Bicycle: I;export declare const BinFull: I;export declare const BinHalf: I;export declare const BinMinusIn: I;export declare const BinPlusIn: I;export declare const Bin: I;export declare const Binocular: I;export declare const BirthdayCake: I;export declare const Bishop: I;export declare const Bitbucket: I;export declare const BitcoinCircle: I;export declare const BitcoinRotateOut: I;export declare const BluetoothTag: I;export declare const Bluetooth: I;export declare const BoldSquare: I;export declare const Bold: I;export declare const Bonfire: I;export declare const BookLock: I;export declare const BookStack: I;export declare const Book: I;export declare const BookmarkBook: I;export declare const BookmarkCircle: I;export declare const Bookmark: I;export declare const BorderBl: I;export declare const BorderBottom: I;export declare const BorderBr: I;export declare const BorderInner: I;export declare const BorderLeft: I;export declare const BorderOut: I;export declare const BorderRight: I;export declare const BorderTl: I;export declare const BorderTop: I;export declare const BorderTr: I;export declare const BounceLeft: I;export declare const BounceRight: I;export declare const BowlingBall: I;export declare const Box3dCenter: I;export declare const Box3dPoint: I;export declare const Box3dThreePoints: I;export declare const BoxIso: I;export declare const Box: I;export declare const BoxingGlove: I;export declare const BrainElectricity: I;export declare const BrainResearch: I;export declare const BrainWarning: I;export declare const Brain: I;export declare const BreadSlice: I;export declare const Bridge3d: I;export declare const BridgeSurface: I;export declare const BrightCrown: I;export declare const BrightStar: I;export declare const BrightnessWindow: I;export declare const Brightness: I;export declare const BubbleDownload: I;export declare const BubbleIncome: I;export declare const BubbleOutcome: I;export declare const BubbleSearch: I;export declare const BubbleStar: I;export declare const BubbleUpload: I;export declare const BubbleWarning: I;export declare const BubbleXmark: I;export declare const Bug: I;export declare const Building: I;export declare const BusGreen: I;export declare const BusStop: I;export declare const Bus: I;export declare const CSquare: I;export declare const CableTag: I;export declare const Calculator: I;export declare const CalendarArrowDown: I;export declare const CalendarArrowUp: I;export declare const CalendarCheck: I;export declare const CalendarMinus: I;export declare const CalendarPlus: I;export declare const CalendarRotate: I;export declare const CalendarXmark: I;export declare const Calendar: I;export declare const Camera: I;export declare const CandlestickChart: I;export declare const Car: I;export declare const CardLock: I;export declare const CardNoAccess: I;export declare const CardReader: I;export declare const CardShield: I;export declare const CardWallet: I;export declare const CartAlt: I;export declare const CartMinus: I;export declare const CartPlus: I;export declare const Cart: I;export declare const Cash: I;export declare const Cell2x2: I;export declare const Cellar: I;export declare const CenterAlign: I;export declare const ChatBubbleCheck: I;export declare const ChatBubbleEmpty: I;export declare const ChatBubbleQuestion: I;export declare const ChatBubbleTranslate: I;export declare const ChatBubbleWarning: I;export declare const ChatBubbleXmark: I;export declare const ChatBubble: I;export declare const ChatLines: I;export declare const ChatMinusIn: I;export declare const ChatPlusIn: I;export declare const CheckCircle: I;export declare const CheckSquare: I;export declare const Check: I;export declare const Chocolate: I;export declare const ChromecastActive: I;export declare const Chromecast: I;export declare const ChurchSide: I;export declare const Church: I;export declare const CigaretteSlash: I;export declare const CinemaOld: I;export declare const CircleSpark: I;export declare const Circle: I;export declare const City: I;export declare const ClipboardCheck: I;export declare const ClockRotateRight: I;export declare const Clock: I;export declare const ClosedCaptionsTag: I;export declare const Closet: I;export declare const CloudBookmark: I;export declare const CloudCheck: I;export declare const CloudDesync: I;export declare const CloudDownload: I;export declare const CloudSquare: I;export declare const CloudSunny: I;export declare const CloudSync: I;export declare const CloudUpload: I;export declare const CloudXmark: I;export declare const Cloud: I;export declare const CodeBracketsSquare: I;export declare const CodeBrackets: I;export declare const Code: I;export declare const Codepen: I;export declare const CoffeeCup: I;export declare const CoinSlash: I;export declare const CoinsSwap: I;export declare const Coins: I;export declare const CollageFrame: I;export declare const Collapse: I;export declare const ColorFilter: I;export declare const ColorPicker: I;export declare const ColorWheel: I;export declare const Combine: I;export declare const Commodity: I;export declare const Community: I;export declare const CompAlignBottom: I;export declare const CompAlignLeft: I;export declare const CompAlignRight: I;export declare const CompAlignTop: I;export declare const CompactDisc: I;export declare const Compass: I;export declare const Component: I;export declare const CompressLines: I;export declare const Compress: I;export declare const Computer: I;export declare const ConstrainedSurface: I;export declare const Consumable: I;export declare const Contactless: I;export declare const ControlSlider: I;export declare const Cookie: I;export declare const CoolingSquare: I;export declare const Copy: I;export declare const Copyright: I;export declare const CornerBottomLeft: I;export declare const CornerBottomRight: I;export declare const CornerTopLeft: I;export declare const CornerTopRight: I;export declare const CpuWarning: I;export declare const Cpu: I;export declare const CrackedEgg: I;export declare const CreativeCommons: I;export declare const CreditCardSlash: I;export declare const CreditCard: I;export declare const CreditCards: I;export declare const Crib: I;export declare const CropRotateBl: I;export declare const CropRotateBr: I;export declare const CropRotateTl: I;export declare const CropRotateTr: I;export declare const Crop: I;export declare const CrownCircle: I;export declare const Crown: I;export declare const Css3: I;export declare const CubeBandage: I;export declare const CubeCutWithCurve: I;export declare const CubeDots: I;export declare const CubeHole: I;export declare const CubeReplaceFace: I;export declare const CubeScan: I;export declare const Cube: I;export declare const CursorPointer: I;export declare const CurveArray: I;export declare const Cut: I;export declare const Cutlery: I;export declare const Cycling: I;export declare const Cylinder: I;export declare const DashFlag: I;export declare const DashboardDots: I;export declare const DashboardSpeed: I;export declare const Dashboard: I;export declare const DataTransferBoth: I;export declare const DataTransferCheck: I;export declare const DataTransferDown: I;export declare const DataTransferUp: I;export declare const DataTransferWarning: I;export declare const DatabaseBackup: I;export declare const DatabaseCheck: I;export declare const DatabaseExport: I;export declare const DatabaseMonitor: I;export declare const DatabaseRestore: I;export declare const DatabaseScriptMinus: I;export declare const DatabaseScriptPlus: I;export declare const DatabaseScript: I;export declare const DatabaseSearch: I;export declare const DatabaseSettings: I;export declare const DatabaseStar: I;export declare const DatabaseStats: I;export declare const DatabaseTag: I;export declare const DatabaseWarning: I;export declare const DatabaseXmark: I;export declare const Database: I;export declare const DeCompress: I;export declare const DeliveryTruck: I;export declare const Delivery: I;export declare const Depth: I;export declare const DesignNib: I;export declare const DesignPencil: I;export declare const Desk: I;export declare const Developer: I;export declare const DewPoint: I;export declare const Dialpad: I;export declare const Diameter: I;export declare const DiceFive: I;export declare const DiceFour: I;export declare const DiceOne: I;export declare const DiceSix: I;export declare const DiceThree: I;export declare const DiceTwo: I;export declare const DimmerSwitch: I;export declare const DirectorChair: I;export declare const Discord: I;export declare const Dishwasher: I;export declare const Display4k: I;export declare const DivideThree: I;export declare const Divide: I;export declare const Dna: I;export declare const Dns: I;export declare const DocMagnifyingGlassIn: I;export declare const DocMagnifyingGlass: I;export declare const DocStarIn: I;export declare const DocStar: I;export declare const DogecoinCircle: I;export declare const DogecoinRotateOut: I;export declare const DollarCircle: I;export declare const Dollar: I;export declare const DomoticWarning: I;export declare const Donate: I;export declare const DotArrowDown: I;export declare const DotArrowLeft: I;export declare const DotArrowRight: I;export declare const DotArrowUp: I;export declare const DotsGrid3x3: I;export declare const DoubleCheck: I;export declare const DownloadCircle: I;export declare const DownloadDataWindow: I;export declare const DownloadSquare: I;export declare const Download: I;export declare const DragHandGesture: I;export declare const Drag: I;export declare const Drawer: I;export declare const Dribbble: I;export declare const DroneChargeFull: I;export declare const DroneChargeHalf: I;export declare const DroneChargeLow: I;export declare const DroneCheck: I;export declare const DroneLanding: I;export declare const DroneRefresh: I;export declare const DroneTakeOff: I;export declare const DroneXmark: I;export declare const Drone: I;export declare const DropletCheck: I;export declare const DropletHalf: I;export declare const DropletSnowFlakeIn: I;export declare const Droplet: I;export declare const EaseCurveControlPoints: I;export declare const EaseInControlPoint: I;export declare const EaseInOut: I;export declare const EaseIn: I;export declare const EaseOutControlPoint: I;export declare const EaseOut: I;export declare const EcologyBook: I;export declare const EditPencil: I;export declare const Edit: I;export declare const Egg: I;export declare const Eject: I;export declare const ElectronicsChip: I;export declare const ElectronicsTransistor: I;export declare const Elevator: I;export declare const Ellipse3dThreePoints: I;export declare const Ellipse3d: I;export declare const EmojiBall: I;export declare const EmojiBlinkLeft: I;export declare const EmojiBlinkRight: I;export declare const EmojiLookDown: I;export declare const EmojiLookLeft: I;export declare const EmojiLookRight: I;export declare const EmojiLookUp: I;export declare const EmojiPuzzled: I;export declare const EmojiQuite: I;export declare const EmojiReally: I;export declare const EmojiSad: I;export declare const EmojiSatisfied: I;export declare const EmojiSingLeftNote: I;export declare const EmojiSingLeft: I;export declare const EmojiSingRightNote: I;export declare const EmojiSingRight: I;export declare const EmojiSurpriseAlt: I;export declare const EmojiSurprise: I;export declare const EmojiTalkingAngry: I;export declare const EmojiTalkingHappy: I;export declare const EmojiThinkLeft: I;export declare const EmojiThinkRight: I;export declare const Emoji: I;export declare const EmptyPage: I;export declare const EnergyUsageWindow: I;export declare const Enlarge: I;export declare const Erase: I;export declare const EthereumCircle: I;export declare const EthereumRotateOut: I;export declare const EuroSquare: I;export declare const Euro: I;export declare const EvChargeAlt: I;export declare const EvCharge: I;export declare const EvPlugCharging: I;export declare const EvPlugXmark: I;export declare const EvPlug: I;export declare const EvStation: I;export declare const EvTag: I;export declare const Exclude: I;export declare const ExpandLines: I;export declare const Expand: I;export declare const Extrude: I;export declare const EyeClosed: I;export declare const Eye: I;export declare const FSquare: I;export declare const Face3dDraft: I;export declare const FaceId: I;export declare const FacebookTag: I;export declare const Facebook: I;export declare const Facetime: I;export declare const Farm: I;export declare const FastArrowDownSquare: I;export declare const FastArrowDown: I;export declare const FastArrowLeftSquare: I;export declare const FastArrowLeft: I;export declare const FastArrowRightSquare: I;export declare const FastArrowRight: I;export declare const FastArrowUpSquare: I;export declare const FastArrowUp: I;export declare const FastDownCircle: I;export declare const FastLeftCircle: I;export declare const FastRightCircle: I;export declare const FastUpCircle: I;export declare const FavouriteBook: I;export declare const FavouriteWindow: I;export declare const Female: I;export declare const Figma: I;export declare const FileNotFound: I;export declare const FillColor: I;export declare const Fillet3d: I;export declare const FilterAlt: I;export declare const FilterListCircle: I;export declare const FilterList: I;export declare const Filter: I;export declare const Finder: I;export declare const FingerprintCheckCircle: I;export declare const FingerprintCircle: I;export declare const FingerprintLockCircle: I;export declare const FingerprintScan: I;export declare const FingerprintSquare: I;export declare const FingerprintWindow: I;export declare const FingerprintXmarkCircle: I;export declare const Fingerprint: I;export declare const FireFlame: I;export declare const Fish: I;export declare const Fishing: I;export declare const Flare: I;export declare const FlashOff: I;export declare const Flash: I;export declare const Flask: I;export declare const FlipReverse: I;export declare const Flip: I;export declare const FloppyDiskArrowIn: I;export declare const FloppyDiskArrowOut: I;export declare const FloppyDisk: I;export declare const Flower: I;export declare const Fog: I;export declare const FolderMinus: I;export declare const FolderPlus: I;export declare const FolderSettings: I;export declare const FolderWarning: I;export declare const Folder: I;export declare const FontQuestion: I;export declare const FootballBall: I;export declare const Football: I;export declare const Forward15Seconds: I;export declare const ForwardMessage: I;export declare const Forward: I;export declare const FrameAltEmpty: I;export declare const FrameAlt: I;export declare const FrameMinusIn: I;export declare const FramePlusIn: I;export declare const FrameSelect: I;export declare const FrameSimple: I;export declare const FrameTool: I;export declare const Frame: I;export declare const Fridge: I;export declare const FxTag: I;export declare const Fx: I;export declare const Gamepad: I;export declare const Garage: I;export declare const GasTankDroplet: I;export declare const GasTank: I;export declare const Gas: I;export declare const GifFormat: I;export declare const Gift: I;export declare const GitBranch: I;export declare const GitCherryPickCommit: I;export declare const GitCommit: I;export declare const GitCompare: I;export declare const GitFork: I;export declare const GitMerge: I;export declare const GitPullRequestClosed: I;export declare const GitPullRequest: I;export declare const Git: I;export declare const GithubCircle: I;export declare const Github: I;export declare const GitlabFull: I;export declare const GlassEmpty: I;export declare const GlassFragile: I;export declare const GlassHalfAlt: I;export declare const GlassHalf: I;export declare const Glasses: I;export declare const Globe: I;export declare const Golf: I;export declare const GoogleCircle: I;export declare const GoogleDocs: I;export declare const GoogleDriveCheck: I;export declare const GoogleDriveSync: I;export declare const GoogleDriveWarning: I;export declare const GoogleDrive: I;export declare const GoogleHome: I;export declare const GoogleOne: I;export declare const Google: I;export declare const Gps: I;export declare const GraduationCap: I;export declare const GraphDown: I;export declare const GraphUp: I;export declare const GridMinus: I;export declare const GridPlus: I;export declare const GridXmark: I;export declare const Group: I;export declare const Gym: I;export declare const HSquare: I;export declare const HalfCookie: I;export declare const HalfMoon: I;export declare const Hammer: I;export declare const HandBrake: I;export declare const HandCard: I;export declare const HandCash: I;export declare const HandContactless: I;export declare const Handbag: I;export declare const HardDrive: I;export declare const Hashtag: I;export declare const Hat: I;export declare const HdDisplay: I;export declare const Hd: I;export declare const Hdr: I;export declare const HeadsetBolt: I;export declare const HeadsetHelp: I;export declare const HeadsetWarning: I;export declare const Headset: I;export declare const HealthShield: I;export declare const Healthcare: I;export declare const HeartArrowDown: I;export declare const Heart: I;export declare const HeatingSquare: I;export declare const HeavyRain: I;export declare const HelpCircle: I;export declare const HelpSquare: I;export declare const Heptagon: I;export declare const HexagonDice: I;export declare const HexagonPlus: I;export declare const Hexagon: I;export declare const HistoricShieldAlt: I;export declare const HistoricShield: I;export declare const HomeAltSlimHoriz: I;export declare const HomeAltSlim: I;export declare const HomeAlt: I;export declare const HomeHospital: I;export declare const HomeSale: I;export declare const HomeSecure: I;export declare const HomeShield: I;export declare const HomeSimpleDoor: I;export declare const HomeSimple: I;export declare const HomeTable: I;export declare const HomeTemperatureIn: I;export declare const HomeTemperatureOut: I;export declare const HomeUser: I;export declare const Home: I;export declare const HorizDistributionLeft: I;export declare const HorizDistributionRight: I;export declare const HorizontalMerge: I;export declare const HorizontalSplit: I;export declare const HospitalCircle: I;export declare const Hospital: I;export declare const HotAirBalloon: I;export declare const Hourglass: I;export declare const HouseRooms: I;export declare const Html5: I;export declare const IceCream: I;export declare const Iconoir: I;export declare const Import: I;export declare const Inclination: I;export declare const Industry: I;export declare const Infinite: I;export declare const InfoCircle: I;export declare const InputField: I;export declare const InputOutput: I;export declare const InputSearch: I;export declare const Instagram: I;export declare const Internet: I;export declare const IntersectAlt: I;export declare const Intersect: I;export declare const IosSettings: I;export declare const IpAddressTag: I;export declare const IrisScan: I;export declare const ItalicSquare: I;export declare const Italic: I;export declare const Jellyfish: I;export declare const JournalPage: I;export declare const Journal: I;export declare const JpegFormat: I;export declare const JpgFormat: I;export declare const KanbanBoard: I;export declare const KeyBack: I;export declare const KeyCommand: I;export declare const KeyMinus: I;export declare const KeyPlus: I;export declare const KeyXmark: I;export declare const Key: I;export declare const KeyframeAlignCenter: I;export declare const KeyframeAlignHorizontal: I;export declare const KeyframeAlignVertical: I;export declare const KeyframeMinusIn: I;export declare const KeyframeMinus: I;export declare const KeyframePlusIn: I;export declare const KeyframePlus: I;export declare const KeyframePosition: I;export declare const Keyframe: I;export declare const KeyframesCouple: I;export declare const KeyframesMinus: I;export declare const KeyframesPlus: I;export declare const Keyframes: I;export declare const Label: I;export declare const Lamp: I;export declare const Language: I;export declare const LaptopCharging: I;export declare const LaptopDevMode: I;export declare const LaptopFix: I;export declare const LaptopWarning: I;export declare const Laptop: I;export declare const LayoutLeft: I;export declare const LayoutRight: I;export declare const LeaderboardStar: I;export declare const Leaderboard: I;export declare const Leaf: I;export declare const Learning: I;export declare const LensPlus: I;export declare const Lens: I;export declare const Lifebelt: I;export declare const LightBulbOff: I;export declare const LightBulbOn: I;export declare const LightBulb: I;export declare const LineSpace: I;export declare const Linear: I;export declare const LinkSlash: I;export declare const LinkXmark: I;export declare const Link: I;export declare const Linkedin: I;export declare const Linux: I;export declare const ListSelect: I;export declare const List: I;export declare const LitecoinCircle: I;export declare const LitecoinRotateOut: I;export declare const LockSlash: I;export declare const LockSquare: I;export declare const Lock: I;export declare const Loft3d: I;export declare const LogIn: I;export declare const LogNoAccess: I;export declare const LogOut: I;export declare const LongArrowDownLeft: I;export declare const LongArrowDownRight: I;export declare const LongArrowLeftDown: I;export declare const LongArrowLeftUp: I;export declare const LongArrowRightDown: I;export declare const LongArrowRightUp: I;export declare const LongArrowUpLeft: I;export declare const LongArrowUpRight: I;export declare const LotOfCash: I;export declare const Lullaby: I;export declare const MacControlKey: I;export declare const MacDock: I;export declare const MacOptionKey: I;export declare const MacOsWindow: I;export declare const MagicWand: I;export declare const MagnetEnergy: I;export declare const Magnet: I;export declare const MailIn: I;export declare const MailOpen: I;export declare const MailOut: I;export declare const Mail: I;export declare const Male: I;export declare const MapPinMinus: I;export declare const MapPinPlus: I;export declare const MapPinXmark: I;export declare const MapPin: I;export declare const MapXmark: I;export declare const Map: I;export declare const MapsArrowDiagonal: I;export declare const MapsArrowXmark: I;export declare const MapsArrow: I;export declare const MapsGoStraight: I;export declare const MapsTurnBack: I;export declare const MapsTurnLeft: I;export declare const MapsTurnRight: I;export declare const MaskSquare: I;export declare const MastercardCard: I;export declare const Mastodon: I;export declare const MathBook: I;export declare const Maximize: I;export declare const Medal1st: I;export declare const Medal: I;export declare const MediaImageFolder: I;export declare const MediaImageList: I;export declare const MediaImagePlus: I;export declare const MediaImageXmark: I;export declare const MediaImage: I;export declare const MediaVideoFolder: I;export declare const MediaVideoList: I;export declare const MediaVideoPlus: I;export declare const MediaVideoXmark: I;export declare const MediaVideo: I;export declare const Medium: I;export declare const Megaphone: I;export declare const MenuScale: I;export declare const Menu: I;export declare const MessageAlert: I;export declare const MessageText: I;export declare const Message: I;export declare const MeterArrowDownRight: I;export declare const Metro: I;export declare const MicrophoneCheck: I;export declare const MicrophoneMinus: I;export declare const MicrophoneMute: I;export declare const MicrophonePlus: I;export declare const MicrophoneSpeaking: I;export declare const MicrophoneWarning: I;export declare const Microphone: I;export declare const Microscope: I;export declare const MinusCircle: I;export declare const MinusHexagon: I;export declare const MinusSquareDashed: I;export declare const MinusSquare: I;export declare const Minus: I;export declare const Mirror: I;export declare const MobileDevMode: I;export declare const MobileFingerprint: I;export declare const MobileVoice: I;export declare const ModernTv4k: I;export declare const ModernTv: I;export declare const MoneySquare: I;export declare const MoonSat: I;export declare const MoreHorizCircle: I;export declare const MoreHoriz: I;export declare const MoreVertCircle: I;export declare const MoreVert: I;export declare const Motorcycle: I;export declare const MouseButtonLeft: I;export declare const MouseButtonRight: I;export declare const MouseScrollWheel: I;export declare const Movie: I;export declare const MpegFormat: I;export declare const MultiBubble: I;export declare const MultiMacOsWindow: I;export declare const MultiWindow: I;export declare const MultiplePagesEmpty: I;export declare const MultiplePagesMinus: I;export declare const MultiplePagesPlus: I;export declare const MultiplePagesXmark: I;export declare const MultiplePages: I;export declare const MusicDoubleNotePlus: I;export declare const MusicDoubleNote: I;export declare const MusicNotePlus: I;export declare const MusicNote: I;export declare const NSquare: I;export declare const NavArrowDown: I;export declare const NavArrowLeft: I;export declare const NavArrowRight: I;export declare const NavArrowUp: I;export declare const NavigatorAlt: I;export declare const Navigator: I;export declare const Neighbourhood: I;export declare const NetworkLeft: I;export declare const NetworkReverse: I;export declare const NetworkRight: I;export declare const Network: I;export declare const NewTab: I;export declare const NintendoSwitch: I;export declare const NoSmokingCircle: I;export declare const NonBinary: I;export declare const Notes: I;export declare const NpmSquare: I;export declare const Npm: I;export declare const Number0Square: I;export declare const Number1Square: I;export declare const Number2Square: I;export declare const Number3Square: I;export declare const Number4Square: I;export declare const Number5Square: I;export declare const Number6Square: I;export declare const Number7Square: I;export declare const Number8Square: I;export declare const Number9Square: I;export declare const NumberedListLeft: I;export declare const NumberedListRight: I;export declare const OSquare: I;export declare const Octagon: I;export declare const OffTag: I;export declare const OilIndustry: I;export declare const Okrs: I;export declare const OnTag: I;export declare const OneFingerSelectHandGesture: I;export declare const OnePointCircle: I;export declare const OpenBook: I;export declare const OpenInBrowser: I;export declare const OpenInWindow: I;export declare const OpenNewWindow: I;export declare const OpenSelectHandGesture: I;export declare const OpenVpn: I;export declare const OrangeHalf: I;export declare const OrangeSliceAlt: I;export declare const OrangeSlice: I;export declare const OrganicFoodSquare: I;export declare const OrganicFood: I;export declare const OrthogonalView: I;export declare const PackageLock: I;export declare const Package: I;export declare const Packages: I;export declare const Pacman: I;export declare const PageDown: I;export declare const PageEdit: I;export declare const PageFlip: I;export declare const PageLeft: I;export declare const PageMinusIn: I;export declare const PageMinus: I;export declare const PagePlusIn: I;export declare const PagePlus: I;export declare const PageRight: I;export declare const PageSearch: I;export declare const PageStar: I;export declare const PageUp: I;export declare const Page: I;export declare const Palette: I;export declare const PanoramaEnlarge: I;export declare const PanoramaReduce: I;export declare const PantsPockets: I;export declare const Pants: I;export declare const Parking: I;export declare const PasswordCheck: I;export declare const PasswordCursor: I;export declare const PasswordXmark: I;export declare const PasteClipboard: I;export declare const PathArrow: I;export declare const PauseWindow: I;export declare const Pause: I;export declare const Paypal: I;export declare const PcCheck: I;export declare const PcFirewall: I;export declare const PcMouse: I;export declare const PcNoEntry: I;export declare const PcWarning: I;export declare const PeaceHand: I;export declare const Peerlist: I;export declare const PenConnectBluetooth: I;export declare const PenConnectWifi: I;export declare const PenTabletConnectUsb: I;export declare const PenTabletConnectWifi: I;export declare const PenTablet: I;export declare const Pentagon: I;export declare const PeopleTag: I;export declare const PercentRotateOut: I;export declare const PercentageCircle: I;export declare const PercentageSquare: I;export declare const Percentage: I;export declare const PerspectiveView: I;export declare const PharmacyCrossCircle: I;export declare const PharmacyCrossTag: I;export declare const PhoneDisabled: I;export declare const PhoneIncome: I;export declare const PhoneMinus: I;export declare const PhoneOutcome: I;export declare const PhonePaused: I;export declare const PhonePlus: I;export declare const PhoneXmark: I;export declare const Phone: I;export declare const PiggyBank: I;export declare const Pillow: I;export declare const PinSlash: I;export declare const Pin: I;export declare const PineTree: I;export declare const Pinterest: I;export declare const Pipe3d: I;export declare const PizzaSlice: I;export declare const PlanetAlt: I;export declare const PlanetSat: I;export declare const Planet: I;export declare const Planimetry: I;export declare const Play: I;export declare const PlaylistPlay: I;export declare const PlaylistPlus: I;export declare const Playlist: I;export declare const PlaystationGamepad: I;export declare const PlugTypeA: I;export declare const PlugTypeC: I;export declare const PlugTypeG: I;export declare const PlugTypeL: I;export declare const PlusCircle: I;export declare const PlusSquareDashed: I;export declare const PlusSquare: I;export declare const Plus: I;export declare const PngFormat: I;export declare const Pocket: I;export declare const Podcast: I;export declare const Pokeball: I;export declare const PolarSh: I;export declare const PositionAlign: I;export declare const Position: I;export declare const Post: I;export declare const Potion: I;export declare const Pound: I;export declare const PrecisionTool: I;export declare const Presentation: I;export declare const Printer: I;export declare const PrintingPage: I;export declare const PriorityDown: I;export declare const PriorityHigh: I;export declare const PriorityMedium: I;export declare const PriorityUp: I;export declare const PrivacyPolicy: I;export declare const PrivateWifi: I;export declare const ProfileCircle: I;export declare const Prohibition: I;export declare const ProjectCurve3d: I;export declare const Puzzle: I;export declare const QrCode: I;export declare const QuestionMark: I;export declare const QuoteMessage: I;export declare const Quote: I;export declare const Radiation: I;export declare const Radius: I;export declare const Rain: I;export declare const RawFormat: I;export declare const ReceiveDollars: I;export declare const ReceiveEuros: I;export declare const ReceivePounds: I;export declare const ReceiveYens: I;export declare const RedoAction: I;export declare const RedoCircle: I;export declare const Redo: I;export declare const Reduce: I;export declare const RefreshCircle: I;export declare const RefreshDouble: I;export declare const Refresh: I;export declare const ReloadWindow: I;export declare const ReminderHandGesture: I;export declare const RepeatOnce: I;export declare const Repeat: I;export declare const ReplyToMessage: I;export declare const Reply: I;export declare const ReportColumns: I;export declare const Reports: I;export declare const Repository: I;export declare const Restart: I;export declare const Rewind: I;export declare const RhombusArrowRight: I;export declare const Rhombus: I;export declare const Rings: I;export declare const Rocket: I;export declare const Rook: I;export declare const RotateCameraLeft: I;export declare const RotateCameraRight: I;export declare const RoundFlask: I;export declare const RoundedMirror: I;export declare const RssFeedTag: I;export declare const RssFeed: I;export declare const RubikCube: I;export declare const RulerArrows: I;export declare const RulerCombine: I;export declare const RulerMinus: I;export declare const RulerPlus: I;export declare const Ruler: I;export declare const Running: I;export declare const Safari: I;export declare const SafeArrowLeft: I;export declare const SafeArrowRight: I;export declare const SafeOpen: I;export declare const Safe: I;export declare const Sandals: I;export declare const ScaleFrameEnlarge: I;export declare const ScaleFrameReduce: I;export declare const ScanBarcode: I;export declare const ScanQrCode: I;export declare const Scanning: I;export declare const Scarf: I;export declare const ScissorAlt: I;export declare const Scissor: I;export declare const Screenshot: I;export declare const SeaAndSun: I;export declare const SeaWaves: I;export declare const SearchEngine: I;export declare const SearchWindow: I;export declare const Search: I;export declare const SecureWindow: I;export declare const SecurityPass: I;export declare const SelectEdge3d: I;export declare const SelectFace3d: I;export declare const SelectPoint3d: I;export declare const SelectWindow: I;export declare const SelectiveTool: I;export declare const SendDiagonal: I;export declare const SendDollars: I;export declare const SendEuros: I;export declare const SendMail: I;export declare const SendPounds: I;export declare const SendYens: I;export declare const Send: I;export declare const ServerConnection: I;export declare const Server: I;export declare const SettingsProfiles: I;export declare const Settings: I;export declare const ShareAndroid: I;export declare const ShareIos: I;export declare const ShieldAlert: I;export declare const ShieldAlt: I;export declare const ShieldBroken: I;export declare const ShieldCheck: I;export declare const ShieldDownload: I;export declare const ShieldEye: I;export declare const ShieldLoading: I;export declare const ShieldMinus: I;export declare const ShieldPlusIn: I;export declare const ShieldQuestion: I;export declare const ShieldSearch: I;export declare const ShieldUpload: I;export declare const ShieldXmark: I;export declare const Shield: I;export declare const ShirtTankTop: I;export declare const Shirt: I;export declare const ShopFourTilesWindow: I;export declare const ShopFourTiles: I;export declare const ShopWindow: I;export declare const Shop: I;export declare const ShoppingBagArrowDown: I;export declare const ShoppingBagArrowUp: I;export declare const ShoppingBagCheck: I;export declare const ShoppingBagMinus: I;export declare const ShoppingBagPlus: I;export declare const ShoppingBagPocket: I;export declare const ShoppingBagWarning: I;export declare const ShoppingBag: I;export declare const ShoppingCodeCheck: I;export declare const ShoppingCodeXmark: I;export declare const ShoppingCode: I;export declare const ShortPantsPockets: I;export declare const ShortPants: I;export declare const ShortcutSquare: I;export declare const Shuffle: I;export declare const SidebarCollapse: I;export declare const SidebarExpand: I;export declare const SigmaFunction: I;export declare const SimpleCart: I;export declare const SineWave: I;export declare const SingleTapGesture: I;export declare const Skateboard: I;export declare const Skateboarding: I;export declare const SkipNext: I;export declare const SkipPrev: I;export declare const SlashSquare: I;export declare const Slash: I;export declare const SleeperChair: I;export declare const Slips: I;export declare const SmallLampAlt: I;export declare const SmallLamp: I;export declare const SmartphoneDevice: I;export declare const Smoking: I;export declare const Snapchat: I;export declare const SnowFlake: I;export declare const Snow: I;export declare const Soap: I;export declare const SoccerBall: I;export declare const Sofa: I;export declare const SoilAlt: I;export declare const Soil: I;export declare const SortDown: I;export declare const SortUp: I;export declare const Sort: I;export declare const SoundHigh: I;export declare const SoundLow: I;export declare const SoundMin: I;export declare const SoundOff: I;export declare const Spades: I;export declare const Spark: I;export declare const Sparks: I;export declare const Sphere: I;export declare const Spiral: I;export declare const SplitArea: I;export declare const SplitSquareDashed: I;export declare const SpockHandGesture: I;export declare const Spotify: I;export declare const Square3dCornerToCorner: I;export declare const Square3dFromCenter: I;export declare const Square3dThreePoints: I;export declare const SquareCursor: I;export declare const SquareDashed: I;export declare const SquareWave: I;export declare const Square: I;export declare const Stackoverflow: I;export declare const StarDashed: I;export declare const StarHalfDashed: I;export declare const Star: I;export declare const StatDown: I;export declare const StatUp: I;export declare const StatsDownSquare: I;export declare const StatsReport: I;export declare const StatsUpSquare: I;export declare const Strategy: I;export declare const Stretching: I;export declare const Strikethrough: I;export declare const Stroller: I;export declare const StyleBorder: I;export declare const SubmitDocument: I;export declare const Substract: I;export declare const Suggestion: I;export declare const Suitcase: I;export declare const SunLight: I;export declare const SvgFormat: I;export declare const Sweep3d: I;export declare const Swimming: I;export declare const SwipeDownGesture: I;export declare const SwipeLeftGesture: I;export declare const SwipeRightGesture: I;export declare const SwipeTwoFingersDownGesture: I;export declare const SwipeTwoFingersLeftGesture: I;export declare const SwipeTwoFingersRightGesture: I;export declare const SwipeTwoFingersUpGesture: I;export declare const SwipeUpGesture: I;export declare const SwitchOff: I;export declare const SwitchOn: I;export declare const SystemRestart: I;export declare const SystemShut: I;export declare const Table2Columns: I;export declare const TableRows: I;export declare const Table: I;export declare const TaskList: I;export declare const TelegramCircle: I;export declare const Telegram: I;export declare const TemperatureDown: I;export declare const TemperatureHigh: I;export declare const TemperatureLow: I;export declare const TemperatureUp: I;export declare const TennisBallAlt: I;export declare const TennisBall: I;export declare const TerminalTag: I;export declare const Terminal: I;export declare const TestTube: I;export declare const TextArrowsUpDown: I;export declare const TextBox: I;export declare const TextMagnifyingGlass: I;export declare const TextSize: I;export declare const TextSquare: I;export declare const Text: I;export declare const Threads: I;export declare const ThreePointsCircle: I;export declare const ThreeStars: I;export declare const ThumbsDown: I;export declare const ThumbsUp: I;export declare const Thunderstorm: I;export declare const TifFormat: I;export declare const TiffFormat: I;export declare const Tiktok: I;export declare const TimeZone: I;export declare const TimerOff: I;export declare const Timer: I;export declare const Tools: I;export declare const Tournament: I;export declare const TowerCheck: I;export declare const TowerNoAccess: I;export declare const TowerWarning: I;export declare const Tower: I;export declare const Trademark: I;export declare const Train: I;export declare const Tram: I;export declare const TransitionDown: I;export declare const TransitionLeft: I;export declare const TransitionRight: I;export declare const TransitionUp: I;export declare const Translate: I;export declare const Trash: I;export declare const Treadmill: I;export declare const Tree: I;export declare const Trekking: I;export declare const Trello: I;export declare const TriangleFlagCircle: I;export declare const TriangleFlagTwoStripes: I;export declare const TriangleFlag: I;export declare const Triangle: I;export declare const Trophy: I;export declare const TruckGreen: I;export declare const TruckLength: I;export declare const Truck: I;export declare const Tunnel: I;export declare const TvFix: I;export declare const TvWarning: I;export declare const Tv: I;export declare const Twitter: I;export declare const TwoPointsCircle: I;export declare const TwoSeaterSofa: I;export declare const Type: I;export declare const UTurnArrowLeft: I;export declare const UTurnArrowRight: I;export declare const Umbrella: I;export declare const UnderlineSquare: I;export declare const Underline: I;export declare const UndoAction: I;export declare const UndoCircle: I;export declare const Undo: I;export declare const UnionAlt: I;export declare const UnionHorizAlt: I;export declare const Union: I;export declare const Unity5: I;export declare const Unity: I;export declare const Unjoin3d: I;export declare const UploadDataWindow: I;export declare const UploadSquare: I;export declare const Upload: I;export declare const Usb: I;export declare const UserBadgeCheck: I;export declare const UserBag: I;export declare const UserCart: I;export declare const UserCircle: I;export declare const UserCrown: I;export declare const UserLove: I;export declare const UserPlus: I;export declare const UserScan: I;export declare const UserSquare: I;export declare const UserStar: I;export declare const UserXmark: I;export declare const User: I;export declare const VeganCircle: I;export declare const VeganSquare: I;export declare const Vegan: I;export declare const VehicleGreen: I;export declare const VerticalMerge: I;export declare const VerticalSplit: I;export declare const Vials: I;export declare const VideoCameraOff: I;export declare const VideoCamera: I;export declare const VideoProjector: I;export declare const View360: I;export declare const ViewColumns2: I;export declare const ViewColumns3: I;export declare const ViewGrid: I;export declare const ViewStructureDown: I;export declare const ViewStructureUp: I;export declare const VoiceCheck: I;export declare const VoiceCircle: I;export declare const VoiceLockCircle: I;export declare const VoiceScan: I;export declare const VoiceSquare: I;export declare const VoiceXmark: I;export declare const Voice: I;export declare const VrTag: I;export declare const VueJs: I;export declare const Waist: I;export declare const Walking: I;export declare const Wallet: I;export declare const WarningCircle: I;export declare const WarningHexagon: I;export declare const WarningSquare: I;export declare const WarningTriangle: I;export declare const WarningWindow: I;export declare const Wash: I;export declare const WashingMachine: I;export declare const WateringSoil: I;export declare const WebWindowEnergyConsumption: I;export declare const WebWindowXmark: I;export declare const WebWindow: I;export declare const WebpFormat: I;export declare const WeightAlt: I;export declare const Weight: I;export declare const Whatsapp: I;export declare const WhiteFlag: I;export declare const WifiOff: I;export declare const WifiSignalNone: I;export declare const WifiTag: I;export declare const WifiWarning: I;export declare const WifiXmark: I;export declare const Wifi: I;export declare const Wind: I;export declare const WindowCheck: I;export declare const WindowLock: I;export declare const WindowNoAccess: I;export declare const WindowTabs: I;export declare const WindowXmark: I;export declare const Windows: I;export declare const Wolf: I;export declare const WrapText: I;export declare const Wrench: I;export declare const Wristwatch: I;export declare const Www: I;export declare const XSquare: I;export declare const X: I;export declare const XboxA: I;export declare const XboxB: I;export declare const XboxX: I;export declare const XboxY: I;export declare const XmarkCircle: I;export declare const XmarkSquare: I;export declare const Xmark: I;export declare const XrayView: I;export declare const YSquare: I;export declare const Yelp: I;export declare const YenSquare: I;export declare const Yen: I;export declare const Yoga: I;export declare const Youtube: I;export declare const ZSquare: I;export declare const ZoomIn: I;export declare const ZoomOut: I;export declare const AdobeAfterEffectsSolid: I;export declare const AdobeIllustratorSolid: I;export declare const AdobeIndesignSolid: I;export declare const AdobeLightroomSolid: I;export declare const AdobePhotoshopSolid: I;export declare const AdobeXdSolid: I;export declare const AirplaySolid: I;export declare const AlarmSolid: I;export declare const AlignBottomBoxSolid: I;export declare const AlignHorizontalCentersSolid: I;export declare const AlignHorizontalSpacingSolid: I;export declare const AlignLeftBoxSolid: I;export declare const AlignRightBoxSolid: I;export declare const AlignTopBoxSolid: I;export declare const AlignVerticalCentersSolid: I;export declare const AlignVerticalSpacingSolid: I;export declare const AppNotificationSolid: I;export declare const AppStoreSolid: I;export declare const AppleShortcutsSolid: I;export declare const ArrowDownCircleSolid: I;export declare const ArrowDownLeftCircleSolid: I;export declare const ArrowDownLeftSquareSolid: I;export declare const ArrowDownRightCircleSolid: I;export declare const ArrowDownRightSquareSolid: I;export declare const ArrowLeftCircleSolid: I;export declare const ArrowLeftTagSolid: I;export declare const ArrowRightCircleSolid: I;export declare const ArrowRightTagSolid: I;export declare const ArrowUpCircleSolid: I;export declare const ArrowUpLeftCircleSolid: I;export declare const ArrowUpLeftSquareSolid: I;export declare const ArrowUpRightCircleSolid: I;export declare const ArrowUpRightSquareSolid: I;export declare const BathroomSolid: I;export declare const BellNotificationSolid: I;export declare const BitcoinCircleSolid: I;export declare const BluetoothTagSolid: I;export declare const BoldSquareSolid: I;export declare const BookSolid: I;export declare const BookmarkCircleSolid: I;export declare const BookmarkSolid: I;export declare const BubbleSearchSolid: I;export declare const BubbleXmarkSolid: I;export declare const BugSolid: I;export declare const CableTagSolid: I;export declare const CalendarArrowDownSolid: I;export declare const CalendarArrowUpSolid: I;export declare const CalendarCheckSolid: I;export declare const CalendarMinusSolid: I;export declare const CalendarPlusSolid: I;export declare const CalendarRotateSolid: I;export declare const CalendarXmarkSolid: I;export declare const CameraSolid: I;export declare const CashSolid: I;export declare const CenterAlignSolid: I;export declare const ChatBubbleCheckSolid: I;export declare const ChatBubbleEmptySolid: I;export declare const ChatBubbleQuestionSolid: I;export declare const ChatBubbleTranslateSolid: I;export declare const ChatBubbleWarningSolid: I;export declare const ChatBubbleXmarkSolid: I;export declare const ChatBubbleSolid: I;export declare const ChatLinesSolid: I;export declare const ChatMinusInSolid: I;export declare const ChatPlusInSolid: I;export declare const CheckCircleSolid: I;export declare const CheckSquareSolid: I;export declare const ClockSolid: I;export declare const ClosedCaptionsTagSolid: I;export declare const CloudSquareSolid: I;export declare const CompAlignBottomSolid: I;export declare const CompAlignLeftSolid: I;export declare const CompAlignRightSolid: I;export declare const CompAlignTopSolid: I;export declare const CompassSolid: I;export declare const ComponentSolid: I;export declare const CoolingSquareSolid: I;export declare const CreditCardSolid: I;export declare const CubeDotsSolid: I;export declare const CubeScanSolid: I;export declare const DatabaseCheckSolid: I;export declare const DatabaseTagSolid: I;export declare const DatabaseXmarkSolid: I;export declare const DatabaseSolid: I;export declare const DesignNibSolid: I;export declare const DiameterSolid: I;export declare const DivideThreeSolid: I;export declare const DivideSolid: I;export declare const DogecoinCircleSolid: I;export declare const DollarCircleSolid: I;export declare const DotsGrid3x3Solid: I;export declare const DownloadCircleSolid: I;export declare const DownloadSquareSolid: I;export declare const DragSolid: I;export declare const DropletSnowFlakeInSolid: I;export declare const DropletSolid: I;export declare const EraseSolid: I;export declare const EthereumCircleSolid: I;export declare const EuroSquareSolid: I;export declare const EyeSolid: I;export declare const FacetimeSolid: I;export declare const FastArrowDownSquareSolid: I;export declare const FastArrowLeftSquareSolid: I;export declare const FastArrowRightSquareSolid: I;export declare const FastArrowUpSquareSolid: I;export declare const FillColorSolid: I;export declare const FilterListCircleSolid: I;export declare const FilterSolid: I;export declare const FlashSolid: I;export declare const FlaskSolid: I;export declare const ForwardSolid: I;export declare const FrameToolSolid: I;export declare const FxTagSolid: I;export declare const GitSolid: I;export declare const GoogleCircleSolid: I;export declare const GraduationCapSolid: I;export declare const HdDisplaySolid: I;export declare const HeadsetBoltSolid: I;export declare const HeadsetWarningSolid: I;export declare const HeadsetSolid: I;export declare const HeartSolid: I;export declare const HeatingSquareSolid: I;export declare const HelpCircleSolid: I;export declare const HelpSquareSolid: I;export declare const HorizDistributionLeftSolid: I;export declare const HorizDistributionRightSolid: I;export declare const HospitalCircleSolid: I;export declare const IceCreamSolid: I;export declare const InfoCircleSolid: I;export declare const ItalicSquareSolid: I;export declare const KeyframeAlignCenterSolid: I;export declare const KeyframeAlignHorizontalSolid: I;export declare const KeyframeAlignVerticalSolid: I;export declare const KeyframeMinusInSolid: I;export declare const KeyframeMinusSolid: I;export declare const KeyframePlusInSolid: I;export declare const KeyframePlusSolid: I;export declare const KeyframePositionSolid: I;export declare const KeyframeSolid: I;export declare const KeyframesCoupleSolid: I;export declare const KeyframesSolid: I;export declare const LabelSolid: I;export declare const LitecoinCircleSolid: I;export declare const LongArrowDownLeftSolid: I;export declare const LongArrowDownRightSolid: I;export declare const LongArrowLeftDownSolid: I;export declare const LongArrowLeftUpSolid: I;export declare const LongArrowRightDownSolid: I;export declare const LongArrowRightUpSolid: I;export declare const LongArrowUpLeftSolid: I;export declare const LongArrowUpRightSolid: I;export declare const MagnetSolid: I;export declare const MailInSolid: I;export declare const MailOpenSolid: I;export declare const MailOutSolid: I;export declare const MailSolid: I;export declare const Medal1stSolid: I;export declare const MedalSolid: I;export declare const MessageAlertSolid: I;export declare const MessageTextSolid: I;export declare const MessageSolid: I;export declare const MicrophoneCheckSolid: I;export declare const MicrophoneMinusSolid: I;export declare const MicrophoneMuteSolid: I;export declare const MicrophonePlusSolid: I;export declare const MicrophoneSpeakingSolid: I;export declare const MicrophoneWarningSolid: I;export declare const MicrophoneSolid: I;export declare const MicroscopeSolid: I;export declare const MinusCircleSolid: I;export declare const MinusSquareSolid: I;export declare const MoneySquareSolid: I;export declare const MultiBubbleSolid: I;export declare const MusicNotePlusSolid: I;export declare const MusicNoteSolid: I;export declare const NavArrowDownSolid: I;export declare const NavArrowLeftSolid: I;export declare const NavArrowRightSolid: I;export declare const NavArrowUpSolid: I;export declare const NetworkLeftSolid: I;export declare const NetworkReverseSolid: I;export declare const NetworkRightSolid: I;export declare const NetworkSolid: I;export declare const Number0SquareSolid: I;export declare const Number1SquareSolid: I;export declare const Number2SquareSolid: I;export declare const Number3SquareSolid: I;export declare const Number4SquareSolid: I;export declare const Number5SquareSolid: I;export declare const Number6SquareSolid: I;export declare const Number7SquareSolid: I;export declare const Number8SquareSolid: I;export declare const Number9SquareSolid: I;export declare const PageDownSolid: I;export declare const PageLeftSolid: I;export declare const PageRightSolid: I;export declare const PageUpSolid: I;export declare const PathArrowSolid: I;export declare const PauseSolid: I;export declare const PeerlistSolid: I;export declare const PercentageCircleSolid: I;export declare const PercentageSquareSolid: I;export declare const PhoneIncomeSolid: I;export declare const PhoneMinusSolid: I;export declare const PhoneOutcomeSolid: I;export declare const PhonePausedSolid: I;export declare const PhonePlusSolid: I;export declare const PhoneXmarkSolid: I;export declare const PhoneSolid: I;export declare const PinSlashSolid: I;export declare const PinSolid: I;export declare const PlanetSolid: I;export declare const PlaySolid: I;export declare const PlusCircleSolid: I;export declare const PlusSquareSolid: I;export declare const PocketSolid: I;export declare const PodcastSolid: I;export declare const PostSolid: I;export declare const PresentationSolid: I;export declare const PriorityDownSolid: I;export declare const PriorityHighSolid: I;export declare const PriorityMediumSolid: I;export declare const PriorityUpSolid: I;export declare const QuoteMessageSolid: I;export declare const QuoteSolid: I;export declare const RadiationSolid: I;export declare const RadiusSolid: I;export declare const RedoCircleSolid: I;export declare const RefreshCircleSolid: I;export declare const ReportsSolid: I;export declare const RewindSolid: I;export declare const RhombusArrowRightSolid: I;export declare const RoundFlaskSolid: I;export declare const RssFeedTagSolid: I;export declare const SendDiagonalSolid: I;export declare const SendMailSolid: I;export declare const SendSolid: I;export declare const ServerConnectionSolid: I;export declare const ServerSolid: I;export declare const ShareAndroidSolid: I;export declare const ShortcutSquareSolid: I;export declare const SkipNextSolid: I;export declare const SkipPrevSolid: I;export declare const SnapchatSolid: I;export declare const SoundHighSolid: I;export declare const SoundLowSolid: I;export declare const SoundMinSolid: I;export declare const SoundOffSolid: I;export declare const SparkSolid: I;export declare const SparksSolid: I;export declare const SquareCursorSolid: I;export declare const StarSolid: I;export declare const StatsDownSquareSolid: I;export declare const StatsUpSquareSolid: I;export declare const StyleBorderSolid: I;export declare const TestTubeSolid: I;export declare const TextSquareSolid: I;export declare const ThreeStarsSolid: I;export declare const TiktokSolid: I;export declare const TimerSolid: I;export declare const TransitionDownSolid: I;export declare const TransitionLeftSolid: I;export declare const TransitionRightSolid: I;export declare const TransitionUpSolid: I;export declare const TrashSolid: I;export declare const UnderlineSquareSolid: I;export declare const UndoCircleSolid: I;export declare const UploadSquareSolid: I;export declare const UsbSolid: I;export declare const VialsSolid: I;export declare const WalletSolid: I;export declare const WarningCircleSolid: I;export declare const WarningSquareSolid: I;export declare const WarningTriangleSolid: I;export declare const WebWindowEnergyConsumptionSolid: I;export declare const WebWindowXmarkSolid: I;export declare const WebWindowSolid: I;export declare const WhatsappSolid: I;export declare const WhiteFlagSolid: I;export declare const WifiSignalNoneSolid: I;export declare const WifiTagSolid: I;export declare const WifiWarningSolid: I;export declare const WindowTabsSolid: I;export declare const WolfSolid: I;export declare const XmarkCircleSolid: I;export declare const XmarkSquareSolid: I;export declare const YenSquareSolid: I;export declare const YoutubeSolid: I;