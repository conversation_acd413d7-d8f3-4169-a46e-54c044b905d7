"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const l=(e,o)=>{const r=n.useContext(C),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M6 3H3V6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M18 3H21V6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M6 21H3V18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M7 18V17C7 14.2386 9.23858 12 12 12V12C14.7614 12 17 14.2386 17 17V18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 12C13.6569 12 15 10.6569 15 9C15 7.34315 13.6569 6 12 6C10.3431 6 9 7.34315 9 9C9 10.6569 10.3431 12 12 12Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M18 21H21V18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},m=c(l);var h=m;export{h as default};
