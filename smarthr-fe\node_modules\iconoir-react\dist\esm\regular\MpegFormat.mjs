"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(e,r,o[r]);return e};import*as t from"react";import{forwardRef as L}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const l=(e,o)=>{const r=t.useContext(c),p=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),t.createElement("path",{d:"M4 6V3.6C4 3.26863 4.26863 3 4.6 3H19.4C19.7314 3 20 3.26863 20 3.6V6",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M4 18V20.4C4 20.7314 4.26863 21 4.6 21H19.4C19.7314 21 20 20.7314 20 20.4V18",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M7.5 15V12M7.5 12V9H10.5L10.5 12H7.5Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M1.5 15L1.5 9L3 12L4.5 9L4.5 15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M16.5 9H13.5L13.5 15L16.5 15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M22.5 9H19.5V15L22.5 15V12.6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M13.5 12H15.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=L(l);var h=C;export{h as default};
