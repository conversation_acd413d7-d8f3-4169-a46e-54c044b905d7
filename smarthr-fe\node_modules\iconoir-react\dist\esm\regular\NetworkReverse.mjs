"use client";var h=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var i=(o,r,t)=>r in o?h(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,n=(o,r)=>{for(var t in r||(r={}))f.call(r,t)&&i(o,t,r[t]);if(s)for(var t of s(r))l.call(r,t)&&i(o,t,r[t]);return o};import*as e from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(o,r)=>{const t=e.useContext(c),m=n(n({},t),o);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},m),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(1 0 0 -1 3 22)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(1 0 0 -1 8.5 7)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(1 0 0 -1 14 22)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M6.5 17V13.5C6.5 12.3954 7.39543 11.5 8.5 11.5H15.5C16.6046 11.5 17.5 12.3954 17.5 13.5V17",stroke:"currentColor"}),e.createElement("path",{d:"M12 11.5V7",stroke:"currentColor"}))},x=p(d);var k=x;export{k as default};
