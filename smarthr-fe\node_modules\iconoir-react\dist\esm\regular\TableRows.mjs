"use client";var V=Object.defineProperty;var n=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var s=(r,o,t)=>o in r?V(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,e=(r,o)=>{for(var t in o||(o={}))p.call(o,t)&&s(r,t,o[t]);if(n)for(var t of n(o))f.call(o,t)&&s(r,t,o[t]);return r};import*as H from"react";import{forwardRef as i}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const l=(r,o)=>{const t=H.useContext(c),m=e(e({},t),r);return H.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),H.createElement("path",{d:"M3 12H7.5H12H16.5H21M3 12V16.5M3 12V7.5M21 12V16.5M21 12V7.5M3 16.5V20.4C3 20.7314 3.26863 21 3.6 21H7.5H12H16.5H20.4C20.7314 21 21 20.7314 21 20.4V16.5M3 16.5H7.5H12H16.5H21M21 7.5V3.6C21 3.26863 20.7314 3 20.4 3H16.5H12H7.5H3.6C3.26863 3 3 3.26863 3 3.6V7.5M21 7.5H16.5H12H7.5H3",stroke:"currentColor"}))},C=i(l);var g=C;export{g as default};
