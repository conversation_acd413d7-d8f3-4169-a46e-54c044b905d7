"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?m(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))C.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const l=(e,o)=>{const r=n.useContext(d),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M4.5 8C8.5 8 11 8 15 8C15 8 15 8 15 8C15 8 20 8 20 12.7059C20 18 15 18 15 18C11.5714 18 9.71429 18 6.28571 18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M7.5 11.5C6.13317 10.1332 5.36683 9.36683 4 8C5.36683 6.63317 6.13317 5.86683 7.5 4.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=f(l);var S=u;export{S as default};
