"use client";var l=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var i=(o,r,t)=>r in o?l(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,n=(o,r)=>{for(var t in r||(r={}))m.call(r,t)&&i(o,t,r[t]);if(s)for(var t of s(r))p.call(r,t)&&i(o,t,r[t]);return o};import*as e from"react";import{forwardRef as c}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const f=(o,r)=>{const t=e.useContext(d),h=n(n({},t),o);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},h),e.createElement("rect",{x:3,y:2,width:7,height:5,rx:.6,stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{x:8.5,y:17,width:7,height:5,rx:.6,stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{x:14,y:2,width:7,height:5,rx:.6,stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M6.5 7V10.5C6.5 11.6046 7.39543 12.5 8.5 12.5H15.5C16.6046 12.5 17.5 11.6046 17.5 10.5V7",stroke:"currentColor"}),e.createElement("path",{d:"M12 12.5V17",stroke:"currentColor"}))},x=c(f);var k=x;export{k as default};
