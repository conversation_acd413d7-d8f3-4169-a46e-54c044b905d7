"use client";var i=Object.defineProperty;var t=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var n=(r,o,e)=>o in r?i(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,C=(r,o)=>{for(var e in o||(o={}))f.call(o,e)&&n(r,e,o[e]);if(t)for(var e of t(o))p.call(o,e)&&n(r,e,o[e]);return r};import*as l from"react";import{forwardRef as s}from"react";import{IconoirContext as u}from"../IconoirContext.mjs";const m=(r,o)=>{const e=l.useContext(u),d=C(C({},e),r);return l.createElement("svg",C({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.25 4C7.25 3.58579 7.58579 3.25 8 3.25L16 3.25C16.4142 3.25 16.75 3.58579 16.75 4C16.75 4.41421 16.4142 4.75 16 4.75L8 4.75C7.58579 4.75 7.25 4.41421 7.25 4Z",fill:"currentColor"}),l.createElement("path",{d:"M9 3.75C9.41421 3.75 9.75 4.08579 9.75 4.5V8.75309C9.75 9.81846 9.13307 10.7134 8.34019 11.2465C7.2076 12.008 6.23223 13.0482 5.58519 14.2538H18.4148C17.7678 13.0482 16.7924 12.008 15.6598 11.2465C14.8669 10.7134 14.25 9.81846 14.25 8.75309V4.5C14.25 4.08579 14.5858 3.75 15 3.75C15.4142 3.75 15.75 4.08579 15.75 4.5V8.75309C15.75 9.2141 16.023 9.6831 16.4968 10.0017C18.8599 11.5905 20.75 14.3104 20.75 17.4616C20.75 18.344 20.6265 19.198 20.3958 20.0058C20.0712 21.1423 18.9936 21.75 17.949 21.75H6.05096C5.00643 21.75 3.92877 21.1423 3.60419 20.0058C3.37348 19.198 3.25 18.344 3.25 17.4616C3.25 14.3104 5.14014 11.5905 7.50324 10.0017C7.97704 9.6831 8.25 9.2141 8.25 8.75309V4.5C8.25 4.08579 8.58579 3.75 9 3.75Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.5117 6.44156C13.8196 6.71865 13.8446 7.19287 13.5675 7.50075L13.5575 7.51186C13.2804 7.81975 12.8062 7.84471 12.4983 7.56761C12.1904 7.29052 12.1654 6.8163 12.4425 6.50842L12.4525 6.49731C12.7296 6.18942 13.2038 6.16447 13.5117 6.44156Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5117 1.44156C11.8196 1.71865 11.8446 2.19287 11.5675 2.50075L11.5575 2.51186C11.2804 2.81975 10.8062 2.84471 10.4983 2.56761C10.1904 2.29052 10.1654 1.8163 10.4425 1.50842L10.4525 1.49731C10.7296 1.18942 11.2038 1.16447 11.5117 1.44156Z",fill:"currentColor"}))},c=s(m);var S=c;export{S as default};
