"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?m(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))C.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const l=(e,o)=>{const r=n.useContext(d),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M9 22C12.866 22 16 18.866 16 15C16 11.134 12.866 8 9 8C5.13401 8 2 11.134 2 15C2 18.866 5.13401 22 9 22Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M15 16C18.866 16 22 12.866 22 9C22 5.13401 18.866 2 15 2C11.134 2 8 5.13401 8 9C8 12.866 11.134 16 15 16Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=f(l);var S=u;export{S as default};
