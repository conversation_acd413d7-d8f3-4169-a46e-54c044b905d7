"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))C.call(o,r)&&p(t,r,o[r]);return t};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as i}from"../IconoirContext.mjs";const u=(t,o)=>{const r=n.useContext(i),l=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},l),n.createElement("path",{d:"M21 12V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H12",stroke:"currentColor",strokeLinecap:"round"}),n.createElement("path",{d:"M20.879 16.9176C21.373 17.2216 21.342 17.9606 20.834 18.0186L18.267 18.3096L17.116 20.6216C16.888 21.0806 16.183 20.8556 16.066 20.2876L14.811 14.1716C14.712 13.6916 15.144 13.3896 15.561 13.6466L20.879 16.9176Z",fill:"currentColor",stroke:"currentColor"}))},w=c(u);var g=w;export{g as default};
