"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var p=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&p(e,r,o[r]);return e};import*as t from"react";import{forwardRef as l}from"react";import{IconoirContext as m}from"../IconoirContext.mjs";const k=(e,o)=>{const r=t.useContext(m),i=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),t.createElement("path",{d:"M21 20L3 14",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M16 10V11",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M12 9V10",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M8 8V9",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M3 21H21V12C21 9.61305 20.0518 7.32387 18.364 5.63604C16.6761 3.94821 14.3869 3 12 3C9.61305 3 7.32387 3.94821 5.63604 5.63604C3.94821 7.32387 3 9.61305 3 12V21Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=l(k);var w=C;export{w as default};
