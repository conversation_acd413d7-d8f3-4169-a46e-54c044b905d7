"use client";var C=Object.defineProperty;var i=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var n=(r,e,o)=>e in r?C(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,l=(r,e)=>{for(var o in e||(e={}))d.call(e,o)&&n(r,o,e[o]);if(i)for(var o of i(e))f.call(e,o)&&n(r,o,e[o]);return r};import*as t from"react";import{forwardRef as s}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const h=(r,e)=>{const o=t.useContext(c),p=l(l({},o),r);return t.createElement("svg",l({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:e},p),t.createElement("g",{clipPath:"url(#clip0_4223_8258)"},t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.4696 9.46973C17.7625 9.1768 18.2373 9.17675 18.5303 9.46961L20.0003 10.9393L21.4696 9.46973C21.7625 9.1768 22.2373 9.17675 22.5303 9.46961C22.8232 9.76247 22.8232 10.2373 22.5304 10.5303L21.061 12L22.5304 13.4697C22.8232 13.7627 22.8232 14.2375 22.5303 14.5304C22.2373 14.8233 21.7625 14.8232 21.4696 14.5303L20.0003 13.0607L18.5303 14.5304C18.2373 14.8233 17.7625 14.8232 17.4696 14.5303C17.1767 14.2373 17.1768 13.7625 17.4697 13.4696L18.9397 12L17.4697 10.5304C17.1768 10.2375 17.1767 9.76266 17.4696 9.46973Z",fill:"currentColor"}),t.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.0367 3.3964C14.2002 2.62923 15.75 3.46373 15.75 4.85741V19.1431C15.75 20.5368 14.2002 21.3713 13.0367 20.6041L7.03762 16.6487C6.99677 16.6218 6.94892 16.6074 6.9 16.6074H4C2.48122 16.6074 1.25 15.3762 1.25 13.8574V10.1431C1.25 8.62434 2.48122 7.39313 4 7.39313H6.9C6.94892 7.39313 6.99677 7.37877 7.03762 7.35184L13.0367 3.3964Z",fill:"currentColor"})),t.createElement("defs",null,t.createElement("clipPath",{id:"clip0_4223_8258"},t.createElement("rect",{width:24,height:24,fill:"white"}))))},m=s(h);var v=m;export{v as default};
