"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?p(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as C}from"react";import{IconoirContext as L}from"../IconoirContext.mjs";const c=(e,o)=>{const r=n.useContext(L),d=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),n.createElement("path",{d:"M4 11.9995C2.78555 12.9117 2 14.3641 2 15.9999C2 18.7613 4.23858 20.9999 7 20.9999C9.76142 20.9999 12 18.7613 12 15.9999C12 14.3641 11.2144 12.9117 10 11.9995",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M4 12V3H10V12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M10 3L12 3",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M10 6L12 6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M10 9H12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M7 14C5.89543 14 5 14.8954 5 16C5 17.1046 5.89543 18 7 18C8.10457 18 9 17.1046 9 16C9 14.8954 8.10457 14 7 14ZM7 14V6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M19 18V6M19 6L21.5 8.5M19 6L16.5 8.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=C(c);var M=l;export{M as default};
