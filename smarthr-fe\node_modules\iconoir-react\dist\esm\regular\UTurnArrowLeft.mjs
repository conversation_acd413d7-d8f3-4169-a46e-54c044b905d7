"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?m(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))c.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as d}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const u=(e,o)=>{const r=n.useContext(l),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M8 14V8.00005C8 5.23862 10.2386 3 13 3C15.7614 3 18 5.23862 18 8.00005V21",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M12 11C10.4379 12.5621 9.5621 13.4379 8 15C6.4379 13.4379 5.5621 12.5621 4 11",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=d(u);var k=C;export{k as default};
