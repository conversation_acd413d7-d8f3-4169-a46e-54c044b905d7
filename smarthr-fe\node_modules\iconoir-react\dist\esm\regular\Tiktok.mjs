"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?m(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))C.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const l=(e,o)=>{const r=n.useContext(d),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M21 8V16C21 18.7614 18.7614 21 16 21H8C5.23858 21 3 18.7614 3 16V8C3 5.23858 5.23858 3 8 3H16C18.7614 3 21 5.23858 21 8Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M10 12C8.34315 12 7 13.3431 7 15C7 16.6569 8.34315 18 10 18C11.6569 18 13 16.6569 13 15V6C13.3333 7 14.6 9 17 9",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=f(l);var k=u;export{k as default};
