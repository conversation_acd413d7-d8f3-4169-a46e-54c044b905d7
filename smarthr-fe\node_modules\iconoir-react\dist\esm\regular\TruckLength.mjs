"use client";var C=Object.defineProperty;var s=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?C(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))d.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))u.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as k}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const c=(t,o)=>{const r=e.useContext(l),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M7 16C8.10457 16 9 15.1046 9 14C9 12.8954 8.10457 12 7 12C5.89543 12 5 12.8954 5 14C5 15.1046 5.89543 16 7 16Z",stroke:"currentColor",strokeMiterlimit:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M17 16C18.1046 16 19 15.1046 19 14C19 12.8954 18.1046 12 17 12C15.8954 12 15 12.8954 15 14C15 15.1046 15.8954 16 17 16Z",stroke:"currentColor",strokeMiterlimit:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M14 14V3.6C14 3.26863 13.7314 3 13.4 3H2.6C2.26863 3 2 3.26863 2 3.6V13.4C2 13.7314 2.26863 14 2.6 14H4.65",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M14 14H9.05005",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M14 6H19.6101C19.8472 6 20.0621 6.13964 20.1584 6.35632L21.9483 10.3836C21.9824 10.4604 22 10.5434 22 10.6273V13.4C22 13.7314 21.7314 14 21.4 14H19.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M14 14H15",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M3 20H20.75M3 20L4.75 21.75M3 20L4.75 18.25M20.75 20L19 21.75M20.75 20L19 18.25",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},m=k(c);var h=m;export{h as default};
