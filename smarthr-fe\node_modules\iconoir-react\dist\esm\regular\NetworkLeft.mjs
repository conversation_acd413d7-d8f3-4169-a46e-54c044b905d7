"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var i=(o,r,t)=>r in o?m(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,n=(o,r)=>{for(var t in r||(r={}))f.call(r,t)&&i(o,t,r[t]);if(s)for(var t of s(r))l.call(r,t)&&i(o,t,r[t]);return o};import*as e from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(o,r)=>{const t=e.useContext(c),h=n(n({},t),o);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},h),e.createElement("rect",{x:2,y:21,width:7,height:5,rx:.6,transform:"rotate(-90 2 21)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{x:17,y:15.5,width:7,height:5,rx:.6,transform:"rotate(-90 17 15.5)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{x:2,y:10,width:7,height:5,rx:.6,transform:"rotate(-90 2 10)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M7 17.5H10.5C11.6046 17.5 12.5 16.6046 12.5 15.5V8.5C12.5 7.39543 11.6046 6.5 10.5 6.5H7",stroke:"currentColor"}),e.createElement("path",{d:"M12.5 12H17",stroke:"currentColor"}))},x=p(d);var k=x;export{k as default};
