"use client";var f=Object.defineProperty;var n=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var l=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))i.call(o,e)&&l(t,e,o[e]);if(n)for(var e of n(o))m.call(o,e)&&l(t,e,o[e]);return t};import*as C from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=C.useContext(c),s=r(r({},e),t);return C.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),C.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 21.75C19.1756 21.75 21.75 19.1756 21.75 16V8C21.75 4.82436 19.1756 2.25 16 2.25H8C4.82436 2.25 2.25 4.82436 2.25 8V16C2.25 19.1756 4.82436 21.75 8 21.75H16ZM11.25 17C11.25 15.6704 10.8101 14.6207 10.0947 13.9053C9.37925 13.1899 8.32956 12.75 7 12.75C6.58579 12.75 6.25 12.4142 6.25 12C6.25 11.5858 6.58579 11.25 7 11.25C8.67044 11.25 10.1207 11.8101 11.1553 12.8447C12.1899 13.8793 12.75 15.3296 12.75 17C12.75 17.4142 12.4142 17.75 12 17.75C11.5858 17.75 11.25 17.4142 11.25 17ZM13.7197 10.2803C15.3101 11.8707 16.25 14.1704 16.25 17C16.25 17.4142 16.5858 17.75 17 17.75C17.4142 17.75 17.75 17.4142 17.75 17C17.75 13.8296 16.6899 11.1292 14.7803 9.21967C12.8707 7.31008 10.1704 6.25 7 6.25C6.58579 6.25 6.25 6.58579 6.25 7C6.25 7.41421 6.58579 7.75 7 7.75C9.82956 7.75 12.1293 8.68991 13.7197 10.2803ZM7.56748 17.5008C7.84457 17.1929 7.81961 16.7187 7.51173 16.4416C7.20385 16.1645 6.72963 16.1894 6.45254 16.4973L6.44254 16.5084C6.16544 16.8163 6.1904 17.2905 6.49828 17.5676C6.80616 17.8447 7.28038 17.8197 7.55748 17.5119L7.56748 17.5008Z",fill:"currentColor"}))},u=p(d);var g=u;export{g as default};
