"use client";var f=Object.defineProperty;var s=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var m=(r,o,t)=>o in r?f(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,e=(r,o)=>{for(var t in o||(o={}))i.call(o,t)&&m(r,t,o[t]);if(s)for(var t of s(o))C.call(o,t)&&m(r,t,o[t]);return r};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const w=(r,o)=>{const t=n.useContext(l),p=e(e({},t),r);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M2.12636 8.32361C1.92567 8.06248 1.97135 7.71868 2.21158 7.53482C5.2903 5.17827 8.55286 4 11.9993 4C15.4457 4 18.7082 5.17827 21.7869 7.53482C22.0388 7.74708 22.0676 8.09272 21.8722 8.32367L12.4174 19.4967C12.3978 19.5199 12.3763 19.5414 12.3531 19.561C12.1222 19.7564 11.7765 19.7276 11.5811 19.4967L2.12636 8.32361Z",stroke:"currentColor"}))},S=c(w);var x=S;export{x as default};
