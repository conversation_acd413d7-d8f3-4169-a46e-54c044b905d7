# SmartHR Application

A full-stack HR management application with a FastAPI backend and React frontend.

## 🏗️ Architecture

- **Backend**: FastAPI (Python) - Semantic matching service with AI capabilities
- **Frontend**: React + Vite + TypeScript - Modern web interface
- **Database**: PostgreSQL with pgvector for embeddings
- **AI Services**: Azure OpenAI, Groq, LangChain integration

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** with pip
- **Node.js 18+** with npm
- **PostgreSQL** database (local or cloud)
- **Azure OpenAI** account and API keys
- **Docker** (optional, for containerized deployment)

### 1. Environment Setup

**Option A: Manual Setup (Recommended)**
1. Copy `.env.example` to `.env` in the root directory
2. Copy `smarthr-be/.env.example` to `smarthr-be/.env`
3. Copy `smarthr-fe/.env.example` to `smarthr-fe/.env`
4. Edit all `.env` files with your actual configuration values

**Option B: Script Setup (Linux/macOS)**
```bash
./setup-env.sh
```

### 2. Start Both Services

**Option A: Manual Startup (Recommended)**

**Terminal 1 - Backend:**
```bash
cd smarthr-be
python -m venv venv
# Windows: venv\Scripts\activate
# Linux/macOS: source venv/bin/activate
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8080 --reload
```

**Terminal 2 - Frontend:**
```bash
cd smarthr-fe
npm install
npm run dev
```

**Option B: Script Startup (Linux/macOS)**
```bash
./start-dev.sh
```

This will start both services:
- **Backend**: http://localhost:8080
- **Frontend**: http://localhost:5173

## 📋 Service Details

### Backend Service (smarthr-be)

- **Port**: 8080
- **API Documentation**: http://localhost:8080/docs
- **Technology**: FastAPI, Python 3.11
- **Features**:
  - Candidate and position management
  - Semantic matching with AI
  - LinkedIn integration
  - WebSocket support
  - Document processing

### Frontend Service (smarthr-fe)

- **Port**: 5173 (development) / 3000 (production)
- **Technology**: React, Vite, TypeScript
- **Features**:
  - Modern React UI with Ant Design
  - Microsoft Authentication (MSAL)
  - Real-time updates via WebSocket
  - File upload and processing
  - Responsive design

## 🔧 Configuration

### Required Environment Variables

#### Backend (.env in smarthr-be/)
```env
# Database
POSTGRES_USER=your-postgres-user
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_HOST=your-postgres-host
POSTGRES_DB=your-postgres-database

# Azure OpenAI
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=your-deployment-name

# AI Services
GROQ_API_KEY=your-groq-api-key

# Authentication
TENANT_ID=your-azure-tenant-id
CLIENT_ID_SMART_HR=your-client-id
```

#### Frontend (.env in smarthr-fe/)
```env
# API Configuration
VITE_BASE_API_URL=http://localhost:8080

# Authentication
VITE_MSAL_CLIENT_ID=your-msal-client-id
VITE_MSAL_AUTHORITY_URL=https://login.microsoftonline.com/your-tenant-id
VITE_MSAL_REDIRECT_URI=http://localhost:5173  # Use 3000 for Docker, 5173 for local dev
```

**Important**: Configure both redirect URIs in your Azure AD app registration:
- `http://localhost:5173` (for local development)
- `http://localhost:3000` (for Docker deployment)

## 🏥 Health Checks

Check if your services are running correctly:

**Windows:**
```powershell
.\health-check.ps1
```

**Linux/macOS:**
```bash
./health-check.sh
```

For detailed health information:
```bash
./health-check.sh detailed
```

## 🐳 Docker Deployment (Experimental)

**Note**: Docker deployment is currently being refined. For production use, we recommend the local development setup above.

```bash
# Copy environment variables
cp .env.example .env
# Edit .env with your values

# Start with Docker Compose
docker compose up --build
```

## 📁 Project Structure

```
ArroyoConsulting/
├── smarthr-be/              # Backend service
│   ├── main.py              # FastAPI application
│   ├── requirements.txt     # Python dependencies
│   ├── routes/              # API routes
│   ├── models/              # Data models
│   ├── services/            # Business logic
│   └── .env                 # Backend configuration
├── smarthr-fe/              # Frontend service
│   ├── src/                 # React source code
│   ├── package.json         # Node.js dependencies
│   ├── vite.config.js       # Vite configuration
│   └── .env                 # Frontend configuration
├── docker-compose.yml       # Docker orchestration
├── start-dev.ps1           # Windows startup script
├── start-dev.sh            # Linux/macOS startup script
├── setup-env.ps1           # Windows environment setup
├── setup-env.sh            # Linux/macOS environment setup
├── health-check.ps1        # Windows health check
└── health-check.sh         # Linux/macOS health check
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Backend (8080): Stop any existing FastAPI services
   - Frontend (5173): Stop any existing Vite dev servers

2. **Database Connection Failed**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database exists and is accessible

3. **Authentication Issues**
   - Verify MSAL configuration in frontend `.env`
   - Check Azure AD app registration settings
   - Ensure redirect URIs are correctly configured

4. **API Connection Failed**
   - Verify backend is running on port 8080
   - Check `VITE_BASE_API_URL` in frontend `.env`
   - Ensure CORS is properly configured

### Getting Help

1. Run health checks: `./health-check.sh detailed`
2. Check service logs in the terminal windows
3. Verify environment configuration
4. Ensure all prerequisites are installed

## 🚀 Next Steps

1. **Configure your environment variables** in the `.env` files
2. **Set up your PostgreSQL database** with the required schema
3. **Configure Azure OpenAI** and other AI services
4. **Set up Microsoft Authentication** for the frontend
5. **Run the health checks** to verify everything is working
6. **Start developing** with both services running!

## 📚 API Documentation

Once the backend is running, visit http://localhost:8080/docs for interactive API documentation.

## 🤝 Development Workflow

1. Start both services: `./start-dev.sh`
2. Make changes to your code
3. Services will auto-reload on file changes
4. Test your changes in the browser
5. Run health checks to verify everything works
6. Commit your changes

---

**Happy coding! 🎉**
