"use client";var f=Object.defineProperty;var s=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var m=(r,o,t)=>o in r?f(r,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[o]=t,e=(r,o)=>{for(var t in o||(o={}))i.call(o,t)&&m(r,t,o[t]);if(s)for(var t of s(o))c.call(o,t)&&m(r,t,o[t]);return r};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const V=(r,o)=>{const t=n.useContext(C),p=e(e({},t),r);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M12 3H20.4C20.7314 3 21 3.26863 21 3.6V20.4C21 20.7314 20.7314 21 20.4 21H12M12 3H3.6C3.26863 3 3 3.26863 3 3.6V20.4C3 20.7314 3.26863 21 3.6 21H12M12 3V21",stroke:"currentColor"}))},w=l(V);var u=w;export{u as default};
