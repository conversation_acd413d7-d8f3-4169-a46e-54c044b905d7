"use client";var f=Object.defineProperty;var n=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var l=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))i.call(o,e)&&l(t,e,o[e]);if(n)for(var e of n(o))m.call(o,e)&&l(t,e,o[e]);return t};import*as C from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=C.useContext(c),s=r(r({},e),t);return C.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),C.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 21.75C19.1756 21.75 21.75 19.1756 21.75 16V8C21.75 4.82436 19.1756 2.25 16 2.25H8C4.82436 2.25 2.25 4.82436 2.25 8V16C2.25 19.1756 4.82436 21.75 8 21.75H16ZM13.7115 5.7629C13.5952 5.41393 13.243 5.20083 12.8799 5.25975C12.5168 5.31867 12.25 5.63223 12.25 6.00007V15.0001C12.25 16.2427 11.2426 17.2501 10 17.2501C8.75736 17.2501 7.75 16.2427 7.75 15.0001C7.75 13.7574 8.75736 12.7501 10 12.7501C10.4142 12.7501 10.75 12.4143 10.75 12.0001C10.75 11.5859 10.4142 11.2501 10 11.2501C7.92893 11.2501 6.25 12.929 6.25 15.0001C6.25 17.0711 7.92893 18.7501 10 18.7501C12.0711 18.7501 13.75 17.0711 13.75 15.0001V8.45786C14.5169 9.17047 15.5973 9.75007 17 9.75007C17.4142 9.75007 17.75 9.41428 17.75 9.00007C17.75 8.58585 17.4142 8.25007 17 8.25007C16.0281 8.25007 15.2888 7.85087 14.7414 7.33115C14.1812 6.79933 13.8434 6.15846 13.7115 5.7629Z",fill:"currentColor"}))},V=p(d);var g=V;export{g as default};
