"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as l}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const r=e.useContext(c),C=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},C),e.createElement("path",{d:"M1 15V9C1 5.68629 3.68629 3 7 3H17C20.3137 3 23 5.68629 23 9V15C23 18.3137 20.3137 21 17 21H7C3.68629 21 1 18.3137 1 15Z",stroke:"currentColor"}),e.createElement("path",{d:"M9 9C10.6569 9 12 10.3431 12 12C12 13.6569 10.6569 15 9 15C7.34315 15 6 13.6569 6 12C6 10.3431 7.34315 9 9 9Z",stroke:"currentColor"}),e.createElement("path",{d:"M14 15V9L18 15V9",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=l(d);var a=u;export{a as default};
