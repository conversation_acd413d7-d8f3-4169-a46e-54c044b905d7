"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(C),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M4.03132 8.91684L19.508 4.58337C19.8835 4.47824 20.2294 4.82421 20.1243 5.19967L15.7908 20.6763C15.6642 21.1284 15.0407 21.1726 14.8517 20.7429L11.6034 13.3605C11.5531 13.246 11.4616 13.1546 11.3471 13.1042L3.96477 9.85598C3.53507 9.66692 3.57926 9.04342 4.03132 8.91684Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=l(d);var h=u;export{h as default};
