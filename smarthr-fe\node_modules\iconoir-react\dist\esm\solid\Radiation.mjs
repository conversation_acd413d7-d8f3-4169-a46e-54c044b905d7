"use client";var d=Object.defineProperty;var t=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var i=(o,C,e)=>C in o?d(o,C,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[C]=e,r=(o,C)=>{for(var e in C||(C={}))p.call(C,e)&&i(o,e,C[e]);if(t)for(var e of t(C))f.call(C,e)&&i(o,e,C[e]);return o};import*as l from"react";import{forwardRef as u}from"react";import{IconoirContext as L}from"../IconoirContext.mjs";const s=(o,C)=>{const e=l.useContext(L),n=r(r({},e),o);return l.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:C},n),l.createElement("g",{clipPath:"url(#clip0_4161_11246)"},l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.7217 15.3033L13.7178 15.3048L13.6928 15.3143C13.6693 15.3231 13.6327 15.3364 13.5849 15.3528C13.489 15.3857 13.3496 15.4303 13.1818 15.4751C12.8392 15.5664 12.4093 15.6498 12 15.6498C11.5907 15.6498 11.1608 15.5664 10.8183 15.4751C10.6504 15.4303 10.511 15.3857 10.4151 15.3528C10.3673 15.3364 10.3307 15.3231 10.3072 15.3143L10.2822 15.3048L10.2785 15.3034C9.94518 15.1704 9.5636 15.292 9.36933 15.5939L6.36933 20.2553C6.25834 20.4278 6.22281 20.6381 6.27101 20.8375C6.31921 21.0368 6.44694 21.2077 6.62446 21.3104C8.2064 22.2255 10.0432 22.749 12 22.749C13.9568 22.749 15.7936 22.2255 17.3755 21.3104C17.5531 21.2077 17.6808 21.0368 17.729 20.8375C17.7772 20.6381 17.7417 20.4278 17.6307 20.2553L14.6307 15.5939C14.4363 15.2919 14.0552 15.17 13.7217 15.3033Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9968 8.85668L14 8.8593L14.0208 8.87622C14.0401 8.89217 14.0699 8.9172 14.108 8.95041C14.1845 9.01702 14.2928 9.11541 14.4155 9.23839C14.6659 9.48939 14.953 9.82005 15.1576 10.1745C15.3623 10.5289 15.5051 10.9429 15.5972 11.2852C15.6424 11.453 15.6734 11.596 15.6929 11.6955C15.7026 11.7451 15.7094 11.7835 15.7135 11.8082L15.7178 11.8346L15.7184 11.8385C15.7699 12.1937 16.066 12.4633 16.4246 12.4807L21.9615 12.748C22.1663 12.7579 22.3663 12.6835 22.5148 12.5421C22.6634 12.4007 22.7475 12.2046 22.7477 11.9995C22.7492 10.172 22.2842 8.31948 21.3058 6.62484C20.3274 4.93021 18.9556 3.60125 17.3721 2.6888C17.1944 2.5864 16.9826 2.56124 16.7858 2.61917C16.5891 2.67709 16.4247 2.81305 16.3308 2.99539L13.7939 7.92419C13.6295 8.24351 13.7146 8.63452 13.9968 8.85668Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.27764 11.839L8.27828 11.8349L8.28257 11.8085C8.28672 11.7838 8.29348 11.7454 8.30319 11.6958C8.32266 11.5963 8.35369 11.4533 8.39885 11.2856C8.49102 10.9432 8.63382 10.5292 8.83845 10.1748C9.04308 9.82036 9.3302 9.4897 9.5806 9.2387C9.70329 9.11572 9.81164 9.01734 9.88807 8.95072C9.92617 8.91752 9.95601 8.89249 9.97534 8.87654L9.99606 8.85962L9.99914 8.85714C10.281 8.63493 10.3665 8.24368 10.2022 7.9245L7.66526 2.9957C7.57141 2.81336 7.40698 2.6774 7.21025 2.61948C7.01352 2.56155 6.80165 2.58672 6.62396 2.68911C5.04048 3.60156 3.6687 4.93052 2.69031 6.62516C1.71191 8.31979 1.24688 10.1723 1.24841 11.9998C1.24858 12.2049 1.33273 12.401 1.48126 12.5424C1.62979 12.6838 1.82974 12.7582 2.03458 12.7483L7.57152 12.481C7.93024 12.4636 8.22632 12.1945 8.27764 11.839Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.75 11.999C14.75 10.4802 13.5188 9.24902 12 9.24902C10.4812 9.24902 9.25 10.4802 9.25 11.999C9.25 13.5178 10.4812 14.749 12 14.749C13.5188 14.749 14.75 13.5178 14.75 11.999Z",fill:"currentColor"})),l.createElement("defs",null,l.createElement("clipPath",{id:"clip0_4161_11246"},l.createElement("rect",{width:24,height:24,fill:"white"}))))},c=u(s);var g=c;export{g as default};
