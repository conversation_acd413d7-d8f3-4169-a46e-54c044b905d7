"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var p=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&p(e,r,o[r]);return e};import*as t from"react";import{forwardRef as C}from"react";import{IconoirContext as k}from"../IconoirContext.mjs";const l=(e,o)=>{const r=t.useContext(k),i=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),t.createElement("path",{d:"M12 21V7C12 5.89543 12.8954 5 14 5H21.4C21.7314 5 22 5.26863 22 5.6V18.7143",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M12 21V7C12 5.89543 11.1046 5 10 5H2.6C2.26863 5 2 5.26863 2 5.6V18.7143",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M14 19L22 19",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M10 19L2 19",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M12 21C12 19.8954 12.8954 19 14 19",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M12 21C12 19.8954 11.1046 19 10 19",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},m=C(l);var V=m;export{V as default};
