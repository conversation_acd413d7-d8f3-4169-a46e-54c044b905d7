import { IController } from "./IController.js";
import { Configuration } from "../config/Configuration.js";
import { InitializeApplicationRequest } from "../request/InitializeApplicationRequest.js";
export declare function createV3Controller(config: Configuration, request?: InitializeApplicationRequest): Promise<IController>;
export declare function createController(config: Configuration): Promise<IController | null>;
//# sourceMappingURL=ControllerFactory.d.ts.map