"use client";var m=Object.defineProperty;var n=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;var l=(r,o,e)=>o in r?m(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))p.call(o,e)&&l(r,e,o[e]);if(n)for(var e of n(o))s.call(o,e)&&l(r,e,o[e]);return r};import*as C from"react";import{forwardRef as i}from"react";import{IconoirContext as V}from"../IconoirContext.mjs";const c=(r,o)=>{const e=C.useContext(V),f=t(t({},e),r);return C.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},f),C.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 5C2.25 3.48122 3.48122 2.25 5 2.25H19C20.5188 2.25 21.75 3.48122 21.75 5V15C21.75 16.5188 20.5188 17.75 19 17.75H7.96125C7.58154 17.75 7.2224 17.9226 6.98516 18.2191L4.65418 21.1328C3.85702 22.1293 2.25 21.5657 2.25 20.2895V5ZM9.48099 10.75C9.42305 10.8439 9.35472 10.9437 9.275 11.05C8.93823 11.499 8.47947 11.9599 7.96967 12.4697C7.67678 12.7626 7.67678 13.2374 7.96967 13.5303C8.26256 13.8232 8.73744 13.8232 9.03033 13.5303L9.05192 13.5087C9.53637 13.0243 10.0678 12.4929 10.475 11.95C10.8823 11.4069 11.25 10.7447 11.25 10V8C11.25 7.03352 10.4665 6.25 9.5 6.25H8.5C7.53351 6.25 6.75 7.03351 6.75 8V9C6.75 9.96649 7.53351 10.75 8.5 10.75H9.48099ZM15.275 11.05C15.3547 10.9437 15.4231 10.8439 15.481 10.75H14.5C13.5335 10.75 12.75 9.96648 12.75 9V8C12.75 7.03352 13.5335 6.25 14.5 6.25H15.5C16.4665 6.25 17.25 7.03352 17.25 8V10C17.25 10.7447 16.8823 11.4069 16.475 11.95C16.0678 12.4929 15.5364 13.0243 15.0519 13.5087L15.0303 13.5303C14.7374 13.8232 14.2626 13.8232 13.9697 13.5303C13.6768 13.2374 13.6768 12.7626 13.9697 12.4697C14.4795 11.9599 14.9382 11.499 15.275 11.05Z",fill:"currentColor"}))},d=i(c);var g=d;export{g as default};
