"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))C.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const L=(t,o)=>{const r=n.useContext(l),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M11.7704 1.09511C11.9174 1.03421 12.0826 1.03421 12.2296 1.09511L19.5486 4.12672C19.6956 4.18761 19.8124 4.30442 19.8733 4.45144L22.9049 11.7704C22.9658 11.9174 22.9658 12.0826 22.9049 12.2296L19.8733 19.5486C19.8124 19.6956 19.6956 19.8124 19.5486 19.8733L12.2296 22.9049C12.0826 22.9658 11.9174 22.9658 11.7704 22.9049L4.45144 19.8733C4.30442 19.8124 4.18761 19.6956 4.12672 19.5486L1.09511 12.2296C1.03421 12.0826 1.03421 11.9174 1.09511 11.7704L4.12672 4.45144C4.18761 4.30442 4.30442 4.18761 4.45144 4.12672L11.7704 1.09511Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},d=c(L);var g=d;export{g as default};
