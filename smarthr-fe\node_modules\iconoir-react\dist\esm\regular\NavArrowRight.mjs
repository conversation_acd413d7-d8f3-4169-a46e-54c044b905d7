"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const u=(t,o)=>{const r=n.useContext(d),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M9 6L15 12L9 18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},w=l(u);var x=w;export{x as default};
