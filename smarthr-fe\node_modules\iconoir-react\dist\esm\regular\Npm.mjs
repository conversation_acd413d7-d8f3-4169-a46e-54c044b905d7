"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var p=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&p(e,r,o[r]);return e};import*as t from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const L=(e,o)=>{const r=t.useContext(l),i=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),t.createElement("path",{d:"M1 8L23 8V15L11 15V17L7.5 17V15L1 15V8Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M7.5 8L7.5 15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M13.5 8V15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M18 11V15",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M5 11V15",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M11 11V12",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M20.5 11V15",stroke:"currentColor",strokeLinecap:"round"}))},V=c(L);var C=V;export{C as default};
