"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))p.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))C.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as f}from"../IconoirContext.mjs";const u=(t,o)=>{const r=n.useContext(f),l=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},l),n.createElement("path",{d:"M16 2H8C4.68629 2 2 4.68629 2 8V16C2 19.3137 4.68629 22 8 22H16C19.3137 22 22 19.3137 22 16V8C22 4.68629 19.3137 2 16 2Z",stroke:"currentColor",strokeMiterlimit:1.5,strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"2 2"}),n.createElement("path",{d:"M16 5H8C6.34315 5 5 6.34315 5 8V16C5 17.6569 6.34315 19 8 19H16C17.6569 19 19 17.6569 19 16V8C19 6.34315 17.6569 5 16 5Z",fill:"currentColor",stroke:"currentColor",strokeMiterlimit:1.5,strokeLinecap:"round",strokeLinejoin:"round"}))},a=c(u);var h=a;export{h as default};
