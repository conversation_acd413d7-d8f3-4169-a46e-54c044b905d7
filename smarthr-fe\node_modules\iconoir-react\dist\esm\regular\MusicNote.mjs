"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as V}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(l),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M12 16V19C12 20.1046 11.1046 21 10 21H9C7.89543 21 7 20.1046 7 19V18C7 16.8954 7.89543 16 9 16H12ZM12 16V8M12 8V4L17 3V7L12 8Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=V(d);var g=u;export{g as default};
