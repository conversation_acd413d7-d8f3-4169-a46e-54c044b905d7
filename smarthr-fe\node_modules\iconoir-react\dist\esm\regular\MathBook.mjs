"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var p=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&p(e,r,o[r]);return e};import*as t from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const C=(e,o)=>{const r=t.useContext(l),i=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",strokeWidth:1.5,height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),t.createElement("path",{d:"M4 19V5C4 3.89543 4.89543 3 6 3H19.4C19.7314 3 20 3.26863 20 3.6V16.7143",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M6 17L20 17",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M6 21L20 21",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M6 21C4.89543 21 4 20.1046 4 19C4 17.8954 4.89543 17 6 17",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M10 10H14",stroke:"currentColor",strokeLinecap:"round"}),t.createElement("path",{d:"M12 13.01L12.01 12.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M12 7.01L12.01 6.99889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},m=c(C);var w=m;export{w as default};
