"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))l.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as d}from"react";import{IconoirContext as m}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(m),C=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},C),e.createElement("circle",{cx:12,cy:12,r:10,stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M7.63262 3.06689C8.98567 3.35733 9.99999 4.56025 9.99999 6.00007C9.99999 7.65693 8.65685 9.00007 6.99999 9.00007C5.4512 9.00007 4.17653 7.82641 4.01685 6.31997",stroke:"currentColor"}),e.createElement("path",{d:"M22 13.0505C21.3647 12.4022 20.4793 12 19.5 12C17.567 12 16 13.567 16 15.5C16 17.2632 17.3039 18.7219 19 18.9646",stroke:"currentColor"}),e.createElement("path",{d:"M14.5 8.51L14.51 8.49889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M10 17C11.1046 17 12 16.1046 12 15C12 13.8954 11.1046 13 10 13C8.89543 13 8 13.8954 8 15C8 16.1046 8.89543 17 10 17Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},f=d(u);var S=f;export{S as default};
