"use client";var f=Object.defineProperty;var C=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var l=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))i.call(o,e)&&l(t,e,o[e]);if(C)for(var e of C(o))m.call(o,e)&&l(t,e,o[e]);return t};import*as n from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=n.useContext(c),s=r(r({},e),t);return n.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M22.75 12C22.75 6.06294 17.9371 1.25 12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12ZM15.4697 15.5303C15.7626 15.8232 16.2374 15.8232 16.5303 15.5303L19.5303 12.5303C19.8232 12.2374 19.8232 11.7626 19.5303 11.4697L16.5303 8.46967C16.2374 8.17678 15.7626 8.17678 15.4697 8.46967C15.1768 8.76256 15.1768 9.23744 15.4697 9.53033L17.1893 11.25H13.5816C13.3007 10.6588 12.6981 10.25 12 10.25C11.0335 10.25 10.25 11.0335 10.25 12C10.25 12.9665 11.0335 13.75 12 13.75C12.6981 13.75 13.3007 13.3412 13.5816 12.75H17.1893L15.4697 14.4697C15.1768 14.7626 15.1768 15.2374 15.4697 15.5303Z",fill:"currentColor"}))},u=p(d);var h=u;export{h as default};
