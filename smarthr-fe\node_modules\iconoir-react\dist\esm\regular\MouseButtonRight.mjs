"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&p(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(C),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M4 10V14C4 18.4183 7.58172 22 12 22C16.4183 22 20 18.4183 20 14V9C20 5.13401 16.866 2 13 2H12C7.58172 2 4 5.58172 4 10Z",stroke:"currentColor",strokeLinecap:"round"}),n.createElement("path",{d:"M12 2V8.4C12 8.73137 12.2686 9 12.6 9H20",stroke:"currentColor",strokeLinecap:"round"}))},u=l(d);var S=u;export{S as default};
