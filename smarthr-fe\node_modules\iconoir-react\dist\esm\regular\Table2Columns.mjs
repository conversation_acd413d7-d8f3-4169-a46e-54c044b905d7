"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))C.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as i}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const r=e.useContext(c),l=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},l),e.createElement("path",{d:"M3 20.4V3.6C3 3.26863 3.26863 3 3.6 3H20.4C20.7314 3 21 3.26863 21 3.6V20.4C21 20.7314 20.7314 21 20.4 21H3.6C3.26863 21 3 20.7314 3 20.4Z",stroke:"currentColor"}),e.createElement("path",{d:"M3 16.5H21",stroke:"currentColor"}),e.createElement("path",{d:"M3 12H21",stroke:"currentColor"}),e.createElement("path",{d:"M21 7.5H3",stroke:"currentColor"}),e.createElement("path",{d:"M12 21V3",stroke:"currentColor"}))},h=i(d);var g=h;export{g as default};
