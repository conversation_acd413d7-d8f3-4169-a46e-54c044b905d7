"use client";var f=Object.defineProperty;var l=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var C=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))i.call(o,e)&&C(t,e,o[e]);if(l)for(var e of l(o))m.call(o,e)&&C(t,e,o[e]);return t};import*as n from"react";import{forwardRef as p}from"react";import{IconoirContext as V}from"../IconoirContext.mjs";const c=(t,o)=>{const e=n.useContext(V),s=r(r({},e),t);return n.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.22222 3.25C2.56093 3.25 1.25 4.62919 1.25 6.28571V17.7143C1.25 19.3708 2.56094 20.75 4.22222 20.75H19.7778C21.4391 20.75 22.75 19.3708 22.75 17.7143V6.28571C22.75 4.6292 21.4391 3.25 19.7778 3.25H4.22222ZM9.75 5.5C9.75 5.08579 9.41421 4.75 9 4.75C8.58579 4.75 8.25 5.08579 8.25 5.5V8C8.25 8.41421 8.58579 8.75 9 8.75H20.25C20.6642 8.75 21 8.41421 21 8C21 7.58579 20.6642 7.25 20.25 7.25H16.25V5.5C16.25 5.08579 15.9142 4.75 15.5 4.75C15.0858 4.75 14.75 5.08579 14.75 5.5V7.25H9.75V5.5Z",fill:"currentColor"}))},d=p(c);var g=d;export{g as default};
