"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?d(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))u.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as V}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const f=(t,o)=>{const r=e.useContext(l),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M12 8L12 16M12 8H8M12 8H16",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M21 13.5V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V13.5M21 10.5V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V10.5",stroke:"currentColor",strokeLinejoin:"round"}),e.createElement("path",{d:"M19.5 13.5V10.5H22.5V13.5H19.5Z",stroke:"currentColor",strokeLinejoin:"round"}),e.createElement("path",{d:"M1.5 13.5V10.5H4.5V13.5H1.5Z",stroke:"currentColor",strokeLinejoin:"round"}))},C=V(f);var H=C;export{H as default};
