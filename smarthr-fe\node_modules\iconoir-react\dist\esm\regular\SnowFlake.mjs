"use client";var i=Object.defineProperty;var M=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var L=(t,o,r)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&L(t,r,o[r]);if(M)for(var r of M(o))p.call(o,r)&&L(t,r,o[r]);return t};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const l=(t,o)=>{const r=n.useContext(c),s=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),n.createElement("path",{d:"M3 7L6.5 9M21 17L17.5 15M12 12L6.5 9M12 12L6.5 15M12 12V5M12 12V18.5M12 12L17.5 15M12 12L17.5 9M12 2V5M12 22V18.5M21 7L17.5 9M3 17L6.5 15M6.5 9L3 10M6.5 9L6 5.5M6.5 15L3 14M6.5 15L6 18.5M12 5L9.5 4M12 5L14.5 4M12 18.5L14.5 20M12 18.5L9.5 20M17.5 15L18 18.5M17.5 15L21 14M17.5 9L21 10M17.5 9L18 5.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},V=f(l);var a=V;export{a as default};
