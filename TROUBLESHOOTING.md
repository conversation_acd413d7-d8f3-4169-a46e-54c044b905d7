# SmartHR Troubleshooting Guide

This guide helps you resolve common issues when running the SmartHR application.

## 🔧 Quick Diagnostics

### Health Check Commands

**Linux/macOS:**
```bash
./health-check.sh detailed
```

**Manual Health Check:**
```bash
# Check backend
curl http://localhost:8080/docs

# Check frontend
curl http://localhost:5173
```

## 🚨 Common Issues

### 1. Port Already in Use

**Symptoms:**
- Error: "Address already in use" or "Port 8080/5173 is already in use"
- Services fail to start

**Solutions:**

**Find and kill processes using the ports:**
```bash
# Windows
netstat -ano | findstr :8080
netstat -ano | findstr :5173
taskkill /PID <PID_NUMBER> /F

# Linux/macOS
lsof -ti:8080 | xargs kill -9
lsof -ti:5173 | xargs kill -9
```

**Use alternative ports:**
```bash
# Backend on different port
uvicorn main:app --host 0.0.0.0 --port 8081 --reload

# Frontend on different port
npm run dev -- --port 3000
```

### 2. Database Connection Issues

**Symptoms:**
- Backend fails to start with database errors
- "Connection refused" or "Authentication failed" errors

**Solutions:**

1. **Verify PostgreSQL is running:**
   ```bash
   # Check if PostgreSQL is running
   pg_isready -h localhost -p 5432
   ```

2. **Check database credentials in `smarthr-be/.env`:**
   ```env
   POSTGRES_USER=your-username
   POSTGRES_PASSWORD=your-password
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5432
   POSTGRES_DB=your-database
   ```

3. **Test database connection:**
   ```bash
   psql -h localhost -U your-username -d your-database
   ```

4. **Create database if it doesn't exist:**
   ```sql
   CREATE DATABASE your-database;
   ```

### 3. Python/Node.js Environment Issues

**Symptoms:**
- "Python not found" or "Node not found"
- Module import errors
- Package installation failures

**Solutions:**

1. **Verify Python installation:**
   ```bash
   python --version  # Should be 3.11+
   pip --version
   ```

2. **Verify Node.js installation:**
   ```bash
   node --version    # Should be 18+
   npm --version
   ```

3. **Backend Python environment:**
   ```bash
   cd smarthr-be
   python -m venv venv
   # Windows: venv\Scripts\activate
   # Linux/macOS: source venv/bin/activate
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **Frontend Node.js environment:**
   ```bash
   cd smarthr-fe
   rm -rf node_modules package-lock.json
   npm install
   ```

### 4. Environment Configuration Issues

**Symptoms:**
- Authentication failures
- API connection errors
- Missing environment variables

**Solutions:**

1. **Check all .env files exist:**
   ```bash
   ls -la .env smarthr-be/.env smarthr-fe/.env
   ```

2. **Verify critical environment variables:**

   **Backend (`smarthr-be/.env`):**
   ```env
   POSTGRES_HOST=localhost
   POSTGRES_USER=your-user
   POSTGRES_PASSWORD=your-password
   POSTGRES_DB=your-database
   AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
   AZURE_OPENAI_API_KEY=your-key
   ```

   **Frontend (`smarthr-fe/.env`):**
   ```env
   VITE_BASE_API_URL=http://localhost:8080
   VITE_MSAL_CLIENT_ID=your-client-id
   VITE_MSAL_AUTHORITY_URL=https://login.microsoftonline.com/your-tenant-id
   ```

3. **Test API connectivity:**
   ```bash
   # From frontend directory, check if backend is reachable
   curl http://localhost:8080/docs
   ```

### 5. Authentication Issues (MSAL)

**Symptoms:**
- Login failures
- Redirect errors
- "Invalid client" errors
- "AADSTS50011: The redirect URI does not match" error

**Solutions:**

1. **Configure Azure AD app registration redirect URIs:**

   In the Azure Portal, go to your app registration and add both redirect URIs:
   - `http://localhost:5173` (for local development)
   - `http://localhost:3000` (for Docker deployment)

   **Steps:**
   - Go to Azure Portal → App registrations → Your app
   - Click "Authentication" in the left menu
   - Under "Redirect URIs", add both URLs above
   - Save the configuration

2. **Match environment configuration to deployment:**

   **For local development (port 5173):**
   ```env
   VITE_MSAL_REDIRECT_URI=http://localhost:5173
   ```

   **For Docker deployment (port 3000):**
   ```env
   VITE_MSAL_REDIRECT_URI=http://localhost:3000
   ```

3. **Verify other MSAL configuration:**
   ```env
   VITE_MSAL_CLIENT_ID=your-client-id
   VITE_MSAL_AUTHORITY_URL=https://login.microsoftonline.com/your-tenant-id
   ```

4. **Clear browser cache and cookies**

5. **Test with different browser or incognito mode**

### 6. CORS Issues

**Symptoms:**
- "CORS policy" errors in browser console
- Frontend cannot reach backend APIs

**Solutions:**

1. **Verify backend CORS configuration** (should already be set in `main.py`):
   ```python
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["*"],  # For development
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )
   ```

2. **Check frontend API URL:**
   ```env
   VITE_BASE_API_URL=http://localhost:8080
   ```

### 7. Docker Issues

**Symptoms:**
- Docker build failures
- Container startup issues
- Service communication problems

**Solutions:**

1. **Clean Docker environment:**
   ```bash
   docker compose down
   docker system prune -f
   docker compose up --build
   ```

2. **Check Docker logs:**
   ```bash
   docker compose logs smarthr-backend
   docker compose logs smarthr-frontend
   ```

3. **Verify environment variables in docker-compose.yml**

4. **Use local development instead:**
   - Docker setup is experimental
   - Manual startup is more reliable for development

## 🔍 Debugging Steps

### Step 1: Verify Prerequisites
```bash
python --version    # 3.11+
node --version      # 18+
npm --version
psql --version      # PostgreSQL client
```

### Step 2: Check Environment Files
```bash
# Verify all .env files exist and have content
cat .env
cat smarthr-be/.env
cat smarthr-fe/.env
```

### Step 3: Test Database Connection
```bash
# From smarthr-be directory
python -c "
import os
from dotenv import load_dotenv
import psycopg2
load_dotenv()
conn = psycopg2.connect(
    host=os.getenv('POSTGRES_HOST'),
    user=os.getenv('POSTGRES_USER'),
    password=os.getenv('POSTGRES_PASSWORD'),
    database=os.getenv('POSTGRES_DB')
)
print('Database connection successful!')
conn.close()
"
```

### Step 4: Test Services Individually

**Backend:**
```bash
cd smarthr-be
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn main:app --host 0.0.0.0 --port 8080 --reload
# Test: curl http://localhost:8080/docs
```

**Frontend:**
```bash
cd smarthr-fe
npm run dev
# Test: curl http://localhost:5173
```

### Step 5: Check Service Communication
```bash
# With both services running, test API calls
curl http://localhost:8080/docs
curl http://localhost:5173
```

## 📞 Getting Help

If you're still experiencing issues:

1. **Check the logs** in your terminal windows
2. **Run health checks** with detailed output
3. **Verify all prerequisites** are installed correctly
4. **Test each service individually** before running together
5. **Check environment configuration** matches your actual setup

## 🔄 Reset Everything

If all else fails, start fresh:

```bash
# Stop all services
# Kill any processes on ports 8080 and 5173

# Clean backend
cd smarthr-be
rm -rf venv __pycache__ .pytest_cache
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate
pip install -r requirements.txt

# Clean frontend
cd ../smarthr-fe
rm -rf node_modules dist .vite
npm install

# Reconfigure environment
cp .env.example .env
cp smarthr-be/.env.example smarthr-be/.env
cp smarthr-fe/.env.example smarthr-fe/.env
# Edit all .env files with your values

# Start services manually
# Terminal 1: cd smarthr-be && uvicorn main:app --reload
# Terminal 2: cd smarthr-fe && npm run dev
```

---

**Still need help?** Check the main README.md for additional setup instructions.
