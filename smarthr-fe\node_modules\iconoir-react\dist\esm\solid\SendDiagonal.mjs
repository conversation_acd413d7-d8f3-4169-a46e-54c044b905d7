"use client";var f=Object.defineProperty;var i=Object.getOwnPropertySymbols;var s=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var n=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,l=(t,o)=>{for(var e in o||(o={}))s.call(o,e)&&n(t,e,o[e]);if(i)for(var e of i(o))m.call(o,e)&&n(t,e,o[e]);return t};import*as r from"react";import{forwardRef as c}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const C=(t,o)=>{const e=r.useContext(d),p=l(l({},e),t);return r.createElement("svg",l({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),r.createElement("g",{clipPath:"url(#clip0_4086_8473)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.84647 7.15123C1.54566 7.21608 1.31498 7.45811 1.26464 7.7617C1.2143 8.06528 1.35452 8.36881 1.6183 8.52729L8.13474 12.4421L14.3544 8.08705C14.6938 7.84947 15.1614 7.93193 15.399 8.27123C15.6366 8.61054 15.5541 9.0782 15.2148 9.31578L8.99537 13.6707L10.4455 21.1339C10.5042 21.436 10.7415 21.6715 11.044 21.7281C11.3465 21.7846 11.6528 21.6506 11.8166 21.3901L22.7919 3.93893C22.9526 3.68349 22.9445 3.35665 22.7714 3.10947C22.5983 2.86228 22.294 2.7429 21.999 2.80649L1.84647 7.15123Z",fill:"currentColor"})),r.createElement("defs",null,r.createElement("clipPath",{id:"clip0_4086_8473"},r.createElement("rect",{width:24,height:24,fill:"white"}))))},h=c(C);var S=h;export{S as default};
