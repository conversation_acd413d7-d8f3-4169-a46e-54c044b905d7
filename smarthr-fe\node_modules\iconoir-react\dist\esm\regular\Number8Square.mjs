"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var C=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&C(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&C(t,r,o[r]);return t};import*as e from"react";import{forwardRef as d}from"react";import{IconoirContext as f}from"../IconoirContext.mjs";const l=(t,o)=>{const r=e.useContext(f),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M3 20.4V3.6C3 3.26863 3.26863 3 3.6 3H20.4C20.7314 3 21 3.26863 21 3.6V20.4C21 20.7314 20.7314 21 20.4 21H3.6C3.26863 21 3 20.7314 3 20.4Z",stroke:"currentColor"}),e.createElement("path",{d:"M12 16C10.6193 16 9.5 15.5 9.5 14C9.5 12.5 10.6193 12 12 12C13.3807 12 14.5 12.5 14.5 14C14.5 15.5 13.3807 16 12 16Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M12 8C10.6193 8 9.5 8.5 9.5 10C9.5 11.5 10.6193 12 12 12C13.3807 12 14.5 11.5 14.5 10C14.5 8.5 13.3807 8 12 8Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=d(l);var a=u;export{a as default};
