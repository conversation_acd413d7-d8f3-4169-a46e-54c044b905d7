"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as l}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(d),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M7 11C7 8.79086 8.79086 7 11 7H13C15.2091 7 17 8.79086 17 11V20.4C17 20.7314 16.7314 21 16.4 21H7.6C7.26863 21 7 20.7314 7 20.4V11Z",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M7 13H17",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M12 7V3M12 3H9M12 3H13",stroke:"currentColor",strokeLinecap:"round"}))},C=l(u);var k=C;export{k as default};
