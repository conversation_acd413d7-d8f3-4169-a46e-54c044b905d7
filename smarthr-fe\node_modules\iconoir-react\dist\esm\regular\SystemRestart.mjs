"use client";var u=Object.defineProperty;var s=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(n,o,r)=>o in n?u(n,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[o]=r,t=(n,o)=>{for(var r in o||(o={}))p.call(o,r)&&i(n,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(n,r,o[r]);return n};import*as e from"react";import{forwardRef as L}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const l=(n,o)=>{const r=e.useContext(c),d=t(t({},r),n);return e.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),e.createElement("path",{d:"M12 2V6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M12 18V22",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M22 12H18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M6 12H2",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M4.92896 4.92896L7.75738 7.75738",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M16.2427 16.2427L19.0711 19.0711",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M19.071 4.92896L16.2426 7.75738",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M7.75732 16.2427L4.9289 19.0711",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},m=L(l);var j=m;export{j as default};
