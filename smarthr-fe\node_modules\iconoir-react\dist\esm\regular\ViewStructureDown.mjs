"use client";var m=Object.defineProperty;var C=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var s=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&s(t,r,o[r]);if(C)for(var r of C(o))l.call(o,r)&&s(t,r,o[r]);return t};import*as e from"react";import{forwardRef as V}from"react";import{IconoirContext as i}from"../IconoirContext.mjs";const c=(t,o)=>{const r=e.useContext(i),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M3 20.4V14.6C3 14.2686 3.26863 14 3.6 14H20.4C20.7314 14 21 14.2686 21 14.6V20.4C21 20.7314 20.7314 21 20.4 21H3.6C3.26863 21 3 20.7314 3 20.4Z",stroke:"currentColor"}),e.createElement("path",{d:"M14 9.4V3.6C14 3.26863 14.2686 3 14.6 3H20.4C20.7314 3 21 3.26863 21 3.6V9.4C21 9.73137 20.7314 10 20.4 10H14.6C14.2686 10 14 9.73137 14 9.4Z",stroke:"currentColor"}),e.createElement("path",{d:"M3 9.4V3.6C3 3.26863 3.26863 3 3.6 3H9.4C9.73137 3 10 3.26863 10 3.6V9.4C10 9.73137 9.73137 10 9.4 10H3.6C3.26863 10 3 9.73137 3 9.4Z",stroke:"currentColor"}))},d=V(c);var g=d;export{g as default};
