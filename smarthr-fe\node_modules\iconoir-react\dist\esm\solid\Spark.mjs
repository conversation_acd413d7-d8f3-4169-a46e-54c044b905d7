"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var m=(t,o,r)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))l.call(o,r)&&m(t,r,o[r]);if(s)for(var r of s(o))p.call(o,r)&&m(t,r,o[r]);return t};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const u=(t,o)=>{const r=n.useContext(C),f=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},f),n.createElement("path",{d:"M3 12C9.26752 12 12 9.36306 12 3C12 9.36306 14.7134 12 21 12C14.7134 12 12 14.7134 12 21C12 14.7134 9.26752 12 3 12Z",fill:"currentColor",stroke:"currentColor",strokeLinejoin:"round"}))},w=c(u);var G=w;export{G as default};
