"use client";var i=Object.defineProperty;var l=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var s=(t,o,e)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))m.call(o,e)&&s(t,e,o[e]);if(l)for(var e of l(o))p.call(o,e)&&s(t,e,o[e]);return t};import*as n from"react";import{forwardRef as C}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=n.useContext(c),f=r(r({},e),t);return n.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},f),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.22222 3.25C2.56093 3.25 1.25 4.62919 1.25 6.28571V17.7143C1.25 19.3708 2.56094 20.75 4.22222 20.75H19.7778C21.4391 20.75 22.75 19.3708 22.75 17.7143V6.28571C22.75 4.6292 21.4391 3.25 19.7778 3.25H4.22222ZM5 6.25C4.58579 6.25 4.25 6.58579 4.25 7C4.25 7.41421 4.58579 7.75 5 7.75H6C6.41421 7.75 6.75 7.41421 6.75 7C6.75 6.58579 6.41421 6.25 6 6.25H5Z",fill:"currentColor"}))},u=C(d);var g=u;export{g as default};
