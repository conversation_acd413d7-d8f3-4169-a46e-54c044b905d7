# SmartHR Environment Setup Script (PowerShell)
# This script helps set up environment files for both services

param(
    [switch]$Interactive,
    [switch]$Help
)

function Show-Help {
    Write-Host "SmartHR Environment Setup Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\setup-env.ps1                 # Copy example files and show instructions"
    Write-Host "  .\setup-env.ps1 -Interactive    # Interactive environment setup"
    Write-Host "  .\setup-env.ps1 -Help           # Show this help"
    Write-Host ""
    Write-Host "This script will:"
    Write-Host "  1. Copy .env.example files to .env files"
    Write-Host "  2. Provide guidance on required configuration"
    Write-Host "  3. Validate environment setup (optional)"
    Write-Host ""
}

function Copy-EnvFiles {
    Write-Host "Setting up environment files..." -ForegroundColor Cyan
    
    # Root .env file
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-Host "✓ Created root .env file" -ForegroundColor Green
        } else {
            Write-Host "⚠ Root .env.example not found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✓ Root .env file already exists" -ForegroundColor Green
    }
    
    # Backend .env file
    if (-not (Test-Path "smarthr-be\.env")) {
        if (Test-Path "smarthr-be\.env.example") {
            Copy-Item "smarthr-be\.env.example" "smarthr-be\.env"
            Write-Host "✓ Created backend .env file" -ForegroundColor Green
        } else {
            Write-Host "⚠ Backend .env.example not found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✓ Backend .env file already exists" -ForegroundColor Green
    }
    
    # Frontend .env file
    if (-not (Test-Path "smarthr-fe\.env")) {
        if (Test-Path "smarthr-fe\.env.example") {
            Copy-Item "smarthr-fe\.env.example" "smarthr-fe\.env"
            Write-Host "✓ Created frontend .env file" -ForegroundColor Green
        } else {
            Write-Host "⚠ Frontend .env.example not found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✓ Frontend .env file already exists" -ForegroundColor Green
    }
}

function Show-ConfigurationGuide {
    Write-Host ""
    Write-Host "Configuration Guide" -ForegroundColor Yellow
    Write-Host "===================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "Required Configuration:" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "1. Database Configuration:" -ForegroundColor White
    Write-Host "   - POSTGRES_USER: Your PostgreSQL username"
    Write-Host "   - POSTGRES_PASSWORD: Your PostgreSQL password"
    Write-Host "   - POSTGRES_HOST: Your PostgreSQL host"
    Write-Host "   - POSTGRES_DB: Your PostgreSQL database name"
    Write-Host ""
    
    Write-Host "2. Azure OpenAI Configuration:" -ForegroundColor White
    Write-Host "   - AZURE_OPENAI_ENDPOINT: Your Azure OpenAI endpoint"
    Write-Host "   - AZURE_OPENAI_API_KEY: Your Azure OpenAI API key"
    Write-Host "   - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS: Your embeddings deployment"
    Write-Host ""
    
    Write-Host "3. Authentication Configuration:" -ForegroundColor White
    Write-Host "   - VITE_MSAL_CLIENT_ID: Your Microsoft Authentication client ID"
    Write-Host "   - VITE_MSAL_AUTHORITY_URL: Your MSAL authority URL"
    Write-Host "   - TENANT_ID: Your Azure tenant ID"
    Write-Host ""
    
    Write-Host "4. API Configuration:" -ForegroundColor White
    Write-Host "   - VITE_BASE_API_URL: Backend API URL (default: http://localhost:8080)"
    Write-Host "   - LUMUS_API_URL: Lumus AI API URL"
    Write-Host "   - GROQ_API_KEY: Groq API key"
    Write-Host ""
    
    Write-Host "Optional Configuration:" -ForegroundColor Cyan
    Write-Host "   - LANGCHAIN_API_KEY: For LangChain tracing"
    Write-Host "   - APPLICATIONINSIGHTS_CONNECTION_STRING: For Azure monitoring"
    Write-Host ""
}

function Test-Configuration {
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    
    $issues = @()
    
    # Check if .env files exist
    if (-not (Test-Path "smarthr-be\.env")) {
        $issues += "Backend .env file missing"
    }
    
    if (-not (Test-Path "smarthr-fe\.env")) {
        $issues += "Frontend .env file missing"
    }
    
    # Check for placeholder values in backend .env
    if (Test-Path "smarthr-be\.env") {
        $backendEnv = Get-Content "smarthr-be\.env" -Raw
        if ($backendEnv -match "your-.*") {
            $issues += "Backend .env contains placeholder values"
        }
    }
    
    # Check for placeholder values in frontend .env
    if (Test-Path "smarthr-fe\.env") {
        $frontendEnv = Get-Content "smarthr-fe\.env" -Raw
        if ($frontendEnv -match "your-.*") {
            $issues += "Frontend .env contains placeholder values"
        }
    }
    
    if ($issues.Count -eq 0) {
        Write-Host "✓ Configuration validation passed" -ForegroundColor Green
    } else {
        Write-Host "⚠ Configuration issues found:" -ForegroundColor Yellow
        foreach ($issue in $issues) {
            Write-Host "  - $issue" -ForegroundColor Yellow
        }
    }
}

function Interactive-Setup {
    Write-Host "Interactive Environment Setup" -ForegroundColor Green
    Write-Host "=============================" -ForegroundColor Green
    Write-Host ""
    
    # Copy files first
    Copy-EnvFiles
    
    Write-Host ""
    Write-Host "Let's configure your environment variables..." -ForegroundColor Cyan
    Write-Host ""
    
    # Backend API URL for frontend
    $backendUrl = Read-Host "Backend API URL for frontend (default: http://localhost:8080)"
    if ([string]::IsNullOrWhiteSpace($backendUrl)) {
        $backendUrl = "http://localhost:8080"
    }
    
    # Update frontend .env
    if (Test-Path "smarthr-fe\.env") {
        $newLine = "VITE_BASE_API_URL=`"$backendUrl`""
        (Get-Content "smarthr-fe\.env") -replace 'VITE_BASE_API_URL=.*', $newLine | Set-Content "smarthr-fe\.env"
        Write-Host "✓ Updated frontend API URL" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Basic configuration completed!" -ForegroundColor Green
    Write-Host "Please edit the .env files manually to add your API keys and database credentials." -ForegroundColor Yellow
}

# Main script logic
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "SmartHR Environment Setup" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

if ($Interactive) {
    Interactive-Setup
} else {
    Copy-EnvFiles
    Show-ConfigurationGuide
    Test-Configuration
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Edit the .env files with your actual configuration values"
Write-Host "2. Run .\start-dev.ps1 to start the development servers"
Write-Host "3. Access the application at http://localhost:5173 (frontend) and http://localhost:8080 (backend)"
