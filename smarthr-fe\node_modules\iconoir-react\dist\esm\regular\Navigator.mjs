"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var l=(r,o,e)=>o in r?i(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))m.call(o,e)&&l(r,e,o[e]);if(s)for(var e of s(o))p.call(o,e)&&l(r,e,o[e]);return r};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const u=(r,o)=>{const e=n.useContext(d),c=t(t({},e),r);return n.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},c),n.createElement("circle",{cx:12,cy:12,r:10,stroke:"currentColor",strokeWidth:1.5}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.8733 15.4753C18.3338 16.345 17.4362 17.3064 16.537 16.9067L11.9994 14.89L7.46178 16.9067C6.56256 17.3064 5.66499 16.345 6.12541 15.4753L11.0838 6.1095C11.4729 5.37447 12.5259 5.37448 12.915 6.1095L17.8733 15.4753Z",stroke:"currentColor"}))},C=f(u);var S=C;export{S as default};
