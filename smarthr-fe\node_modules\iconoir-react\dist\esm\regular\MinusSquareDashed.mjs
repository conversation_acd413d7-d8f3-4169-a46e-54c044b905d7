"use client";var u=Object.defineProperty;var s=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(n,o,r)=>o in n?u(n,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[o]=r,t=(n,o)=>{for(var r in o||(o={}))p.call(o,r)&&i(n,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(n,r,o[r]);return n};import*as e from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const L=(n,o)=>{const r=e.useContext(l),d=t(t({},r),n);return e.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),e.createElement("path",{d:"M7 4H4V7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M8 12H12H16",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M4 11V13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M11 4H13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M11 20H13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M20 11V13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M17 4H20V7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M7 20H4V17",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M17 20H20V17",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},a=c(L);var V=a;export{V as default};
