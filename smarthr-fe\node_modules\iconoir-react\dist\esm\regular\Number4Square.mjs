"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&p(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(C),i=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),n.createElement("path",{d:"M3 20.4V3.6C3 3.26863 3.26863 3 3.6 3H20.4C20.7314 3 21 3.26863 21 3.6V20.4C21 20.7314 20.7314 21 20.4 21H3.6C3.26863 21 3 20.7314 3 20.4Z",stroke:"currentColor"}),n.createElement("path",{d:"M13.5 16V8L9 13.5H15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},u=l(d);var a=u;export{a as default};
