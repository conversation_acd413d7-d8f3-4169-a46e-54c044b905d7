# SmartHR Application Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# BACKEND CONFIGURATION (smarthr-be)
# =============================================================================

# Azure OpenAI Configuration
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS="your-embeddings-deployment-name"
AZURE_OPENAI_ENDPOINT="https://your-openai-endpoint.openai.azure.com/"
AZURE_OPENAI_API_KEY="your-azure-openai-api-key"
OPENAI_API_VERSION="2024-12-01-preview"

# Database Configuration
POSTGRES_USER="your-postgres-user"
POSTGRES_PASSWORD="your-postgres-password"
POSTGRES_HOST="your-postgres-host"
POSTGRES_PORT=5432
POSTGRES_DB="your-postgres-database"

# AI Services
GROQ_API_KEY="your-groq-api-key"

# LangChain Configuration (Optional)
LANGCHAIN_TRACING_V2="true"
LANGCHAIN_API_KEY="your-langchain-api-key"

# Azure Application Insights (Optional - leave empty to disable)
APPLICATIONINSIGHTS_CONNECTION_STRING=""

# Model Configuration
DEFAULT_MODELS_ORDER='["gpt-4o", "gpt-4o-mini", "llama4-pro", "llama4-light"]'
POSITION_MATCH_MODELS_ORDER='["gpt-4o", "gpt-4o-mini","llama4-pro","llama4-light","llama33-70b"]'

# External APIs
LUMUS_API_URL="https://your-lumus-api-url.com"
LUMUS_API_TIMEOUT=360

# Authentication
TENANT_ID="your-azure-tenant-id"
CLIENT_ID_SMART_HR="your-smart-hr-client-id"
CLIENT_ID_POWER_APP="your-power-app-client-id"
CLIENT_ID_FLOW_HR="your-flow-hr-client-id"

# API Configuration
API_CALLBACK_URL="http://localhost:8080/candidate/lumus/callback"
DEFAULT_INTERNAL_PROJECT_ID="your-default-project-id"
PAGES_ASYNC_THRESHOLD=5
MAXIMUM_NUMBER_OF_MATCHES=50

# =============================================================================
# FRONTEND CONFIGURATION (smarthr-fe)
# =============================================================================

# Backend API Configuration
VITE_BASE_API_URL="http://localhost:8080"

# Microsoft Authentication Library (MSAL) Configuration
VITE_MSAL_CLIENT_ID="your-msal-client-id"
VITE_MSAL_AUTHORITY_URL="https://your-tenant.ciamlogin.com/"
VITE_MSAL_REDIRECT_URI="http://localhost:3000"

# External APIs
VITE_LUMUSAI_API_URL="https://your-lumus-api-url.com"

# File Upload Configuration
VITE_UPLOAD_MAX_FILES=5
VITE_UPLOAD_MAX_FILES_SIZE=2097152
