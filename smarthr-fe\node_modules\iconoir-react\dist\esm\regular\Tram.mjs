"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?d(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as C}from"react";import{IconoirContext as L}from"../IconoirContext.mjs";const c=(t,o)=>{const r=e.useContext(L),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M15 16.01L15.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M9 16.01L9.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M13 6H15C17.7614 6 20 8.23858 20 11V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V11C4 8.23858 6.23858 6 9 6H13ZM13 6L14 2M14 2H17M14 2H7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M10.5 20L8.5 22.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M13.5 20L15.5 22.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M16.5 20L18.5 22.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M7.5 20L5.5 22.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M9.6087 9H14.3913C15.832 9 17 10.168 17 11.6087C17 11.8248 16.8248 12 16.6087 12H7.3913C7.17519 12 7 11.8248 7 11.6087C7 10.168 8.16795 9 9.6087 9Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=C(c);var M=l;export{M as default};
