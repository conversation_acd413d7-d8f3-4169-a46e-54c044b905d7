"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?p(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))L.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as k}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const C=(e,o)=>{const r=n.useContext(c),d=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),n.createElement("path",{d:"M3 5L15 5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M20.5 7L20.5 3L19 4.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M21 14L19 14L20.9047 11.0371C20.9669 10.9403 21.0021 10.8268 20.9771 10.7145C20.9193 10.4557 20.716 10 20 10C19 10 19 10.8889 19 10.8889C19 10.8889 19 10.8889 19 10.8889L19 11.1111",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M19.5 19L20 19C20.5523 19 21 19.4477 21 20V20C21 20.5523 20.5523 21 20 21L19 21",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M19 17L21 17L19.5 19",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M3 12L15 12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M3 19L15 19",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=k(C);var w=l;export{w as default};
