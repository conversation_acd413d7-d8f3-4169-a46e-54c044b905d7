"use client";var f=Object.defineProperty;var s=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,C=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))i.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))C.call(o,r)&&p(t,r,o[r]);return t};import*as n from"react";import{forwardRef as c}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(l),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M21 12V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5V19C3 20.1046 3.89543 21 5 21H12",stroke:"currentColor",strokeLinecap:"round"}),n.createElement("path",{d:"M20.879 16.9171C21.373 17.2211 21.342 17.9601 20.834 18.0181L18.267 18.3091L17.116 20.6211C16.888 21.0801 16.183 20.8551 16.066 20.2871L14.811 14.1711C14.712 13.6911 15.144 13.3891 15.561 13.6461L20.879 16.9171Z",stroke:"currentColor"}))},u=c(d);var a=u;export{a as default};
