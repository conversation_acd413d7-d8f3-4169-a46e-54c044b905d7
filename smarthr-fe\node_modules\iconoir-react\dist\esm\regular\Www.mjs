"use client";var u=Object.defineProperty;var s=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var i=(n,o,r)=>o in n?u(n,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[o]=r,t=(n,o)=>{for(var r in o||(o={}))p.call(o,r)&&i(n,r,o[r]);if(s)for(var r of s(o))L.call(o,r)&&i(n,r,o[r]);return n};import*as e from"react";import{forwardRef as k}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const c=(n,o)=>{const r=e.useContext(C),d=t(t({},r),n);return e.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},d),e.createElement("path",{d:"M3.33789 17C5.06694 19.989 8.29866 22 12.0001 22C15.7015 22 18.9332 19.989 20.6622 17",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M3.33789 7C5.06694 4.01099 8.29866 2 12.0001 2C15.7015 2 18.9332 4.01099 20.6622 7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M13 21.9506C13 21.9506 14.4079 20.0966 15.2947 16.9999",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M13 2.04932C13 2.04932 14.4079 3.90328 15.2947 7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M11 21.9506C11 21.9506 9.59215 20.0966 8.70532 16.9999",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M11 2.04932C11 2.04932 9.59215 3.90328 8.70532 7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M9 10L10.5 15L12 10L13.5 15L15 10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M1 10L2.5 15L4 10L5.5 15L7 10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M17 10L18.5 15L20 10L21.5 15L23 10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=k(c);var j=l;export{j as default};
