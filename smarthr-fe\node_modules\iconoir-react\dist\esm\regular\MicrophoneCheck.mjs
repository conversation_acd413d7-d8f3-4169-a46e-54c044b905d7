"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?d(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as m}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const f=(t,o)=>{const r=e.useContext(l),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M15.5 20.5L17.5 22.5L22.5 17.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("rect",{x:5,y:2,width:6,height:12,rx:3,stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M1 10V11C1 14.866 4.13401 18 8 18V18V18C11.866 18 15 14.866 15 11V10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M8 18V22M8 22H5M8 22H11",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},h=m(f);var x=h;export{x as default};
