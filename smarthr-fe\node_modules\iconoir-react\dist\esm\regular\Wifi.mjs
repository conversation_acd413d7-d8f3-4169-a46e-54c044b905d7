"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(e,r,o[r]);return e};import*as t from"react";import{forwardRef as m}from"react";import{IconoirContext as k}from"../IconoirContext.mjs";const l=(e,o)=>{const r=t.useContext(k),p=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),t.createElement("path",{d:"M12 19.51L12.01 19.4989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M2 8C8 3.5 16 3.5 22 8",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M5 12C9 9 15 9 19 12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M8.5 15.5C10.7504 14.1 13.2498 14.0996 15.5001 15.5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},f=m(l);var w=f;export{w as default};
