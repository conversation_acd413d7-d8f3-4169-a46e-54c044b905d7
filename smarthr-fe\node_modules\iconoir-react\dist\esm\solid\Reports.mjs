"use client";var m=Object.defineProperty;var i=Object.getOwnPropertySymbols;var s=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var l=(t,r,o)=>r in t?m(t,r,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[r]=o,n=(t,r)=>{for(var o in r||(r={}))s.call(r,o)&&l(t,o,r[o]);if(i)for(var o of i(r))c.call(r,o)&&l(t,o,r[o]);return t};import*as e from"react";import{forwardRef as h}from"react";import{IconoirContext as p}from"../IconoirContext.mjs";const x=(t,r)=>{const o=e.useContext(p),f=n(n({},o),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},f),e.createElement("rect",{x:16,y:3,width:5,height:18,rx:2,fill:"currentColor"}),e.createElement("rect",{x:9.5,y:9,width:5,height:12,rx:2,fill:"currentColor"}),e.createElement("rect",{x:3,y:16,width:5,height:5,rx:2,fill:"currentColor"}))},w=h(x);var S=w;export{S as default};
