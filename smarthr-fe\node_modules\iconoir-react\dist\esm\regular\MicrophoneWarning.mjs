"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))c.call(o,r)&&i(e,r,o[r]);return e};import*as t from"react";import{forwardRef as k}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const m=(e,o)=>{const r=t.useContext(l),p=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),t.createElement("path",{d:"M21 14L21 18",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M21 22.01L21.01 21.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("rect",{x:7,y:2,width:6,height:12,rx:3,stroke:"currentColor",strokeWidth:1.5}),t.createElement("path",{d:"M3 10V11C3 14.866 6.13401 18 10 18V18V18C13.866 18 17 14.866 17 11V10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M10 18V22M10 22H7M10 22H13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},h=k(m);var w=h;export{w as default};
