"use client";var f=Object.defineProperty;var s=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))l.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))i.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as C}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const V=(t,o)=>{const r=e.useContext(c),m=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),e.createElement("path",{d:"M4 16.9865V7.01353C4 6.71792 4.21531 6.46636 4.50737 6.42072L19.3074 4.10822C19.6713 4.05137 20 4.33273 20 4.70103V19.299C20 19.6673 19.6713 19.9486 19.3074 19.8918L4.50737 17.5793C4.21531 17.5336 4 17.2821 4 16.9865Z",stroke:"currentColor"}),e.createElement("path",{d:"M4 12H20",stroke:"currentColor"}),e.createElement("path",{d:"M10.5 5.5V18.5",stroke:"currentColor"}))},d=C(V);var g=d;export{g as default};
