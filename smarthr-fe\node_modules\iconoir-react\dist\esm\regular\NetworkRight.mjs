"use client";var h=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var i=(o,r,t)=>r in o?h(o,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[r]=t,n=(o,r)=>{for(var t in r||(r={}))f.call(r,t)&&i(o,t,r[t]);if(s)for(var t of s(r))l.call(r,t)&&i(o,t,r[t]);return o};import*as e from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(o,r)=>{const t=e.useContext(c),m=n(n({},t),o);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},m),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(0 -1 -1 0 22 21)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(0 -1 -1 0 7 15.5)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("rect",{width:7,height:5,rx:.6,transform:"matrix(0 -1 -1 0 22 10)",stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M17 17.5H13.5C12.3954 17.5 11.5 16.6046 11.5 15.5V8.5C11.5 7.39543 12.3954 6.5 13.5 6.5H17",stroke:"currentColor"}),e.createElement("path",{d:"M11.5 12H7",stroke:"currentColor"}))},x=p(d);var k=x;export{k as default};
