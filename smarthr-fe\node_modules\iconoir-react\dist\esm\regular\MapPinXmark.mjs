"use client";var C=Object.defineProperty;var s=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?C(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))l.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))m.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as c}from"react";import{IconoirContext as f}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(f),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M16 9.2C16 13.1765 9 20 9 20C9 20 2 13.1765 2 9.2C2 5.22355 5.13401 2 9 2C12.866 2 16 5.22355 16 9.2Z",stroke:"currentColor"}),e.createElement("path",{d:"M9 10C9.55228 10 10 9.55228 10 9C10 8.44772 9.55228 8 9 8C8.44772 8 8 8.44772 8 9C8 9.55228 8.44772 10 9 10Z",fill:"currentColor",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M16.8791 21.1213L19.0005 19M21.1218 16.8787L19.0005 19M19.0005 19L16.8791 16.8787M19.0005 19L21.1218 21.1213",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},d=c(u);var a=d;export{a as default};
