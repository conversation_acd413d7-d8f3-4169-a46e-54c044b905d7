"use client";var p=Object.defineProperty;var l=Object.getOwnPropertySymbols;var s=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var f=(r,o,e)=>o in r?p(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))s.call(o,e)&&f(r,e,o[e]);if(l)for(var e of l(o))i.call(o,e)&&f(r,e,o[e]);return r};import*as n from"react";import{forwardRef as C}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(r,o)=>{const e=n.useContext(c),m=t(t({},e),r);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.29106 3.3088C3.00745 3.18938 2.67967 3.25533 2.4643 3.47514C2.24894 3.69495 2.1897 4.02401 2.31488 4.30512L5.40752 11.25H13C13.4142 11.25 13.75 11.5858 13.75 12C13.75 12.4142 13.4142 12.75 13 12.75H5.40754L2.31488 19.6949C2.1897 19.976 2.24894 20.3051 2.4643 20.5249C2.67967 20.7447 3.00745 20.8107 3.29106 20.6912L22.2911 12.6913C22.5692 12.5742 22.75 12.3018 22.75 12C22.75 11.6983 22.5692 11.4259 22.2911 11.3088L3.29106 3.3088Z",fill:"currentColor"}))},u=C(d);var x=u;export{x as default};
