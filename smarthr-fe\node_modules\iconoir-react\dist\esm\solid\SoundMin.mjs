"use client";var p=Object.defineProperty;var n=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var i=(r,o,e)=>o in r?p(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))C.call(o,e)&&i(r,e,o[e]);if(n)for(var e of n(o))d.call(o,e)&&i(r,e,o[e]);return r};import*as l from"react";import{forwardRef as s}from"react";import{IconoirContext as m}from"../IconoirContext.mjs";const c=(r,o)=>{const e=l.useContext(m),f=t(t({},e),r);return l.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},f),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.5367 3.3964C15.7002 2.62923 17.25 3.46373 17.25 4.85741V19.1431C17.25 20.5368 15.7002 21.3713 14.5367 20.6041L8.53762 16.6487C8.49677 16.6218 8.44892 16.6074 8.4 16.6074H5.5C3.98122 16.6074 2.75 15.3762 2.75 13.8574V10.1431C2.75 8.62434 3.98122 7.39313 5.5 7.39313H8.4C8.44892 7.39313 8.49677 7.37877 8.53762 7.35184L14.5367 3.3964Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.5 8.25C20.9142 8.25 21.25 8.58579 21.25 9L21.25 15C21.25 15.4142 20.9142 15.75 20.5 15.75C20.0858 15.75 19.75 15.4142 19.75 15L19.75 9C19.75 8.58579 20.0858 8.25 20.5 8.25Z",fill:"currentColor"}))},u=s(c);var S=u;export{S as default};
