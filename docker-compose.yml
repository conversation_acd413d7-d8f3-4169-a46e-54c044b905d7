version: '3.8'

services:
  # Backend Service
  smarthr-backend:
    build:
      context: ./smarthr-be
      dockerfile: Dockerfile
    container_name: smarthr-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # Azure OpenAI Configuration
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - OPENAI_API_VERSION=${OPENAI_API_VERSION}
      
      # Database Configuration
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      
      # AI Services
      - GROQ_API_KEY=${GROQ_API_KEY}
      
      # LangChain Configuration
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      
      # Azure Application Insights (optional)
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING:-}
      
      # Model Configuration
      - DEFAULT_MODELS_ORDER=${DEFAULT_MODELS_ORDER}
      - POSITION_MATCH_MODELS_ORDER=${POSITION_MATCH_MODELS_ORDER}
      
      # External APIs
      - LUMUS_API_URL=${LUMUS_API_URL}
      - LUMUS_API_TIMEOUT=${LUMUS_API_TIMEOUT}
      
      # Authentication
      - TENANT_ID=${TENANT_ID}
      - CLIENT_ID_SMART_HR=${CLIENT_ID_SMART_HR}
      - CLIENT_ID_POWER_APP=${CLIENT_ID_POWER_APP}
      - CLIENT_ID_FLOW_HR=${CLIENT_ID_FLOW_HR}
      
      # API Configuration
      - API_CALLBACK_URL=${API_CALLBACK_URL}
      - DEFAULT_INTERNAL_PROJECT_ID=${DEFAULT_INTERNAL_PROJECT_ID}
      - PAGES_ASYNC_THRESHOLD=${PAGES_ASYNC_THRESHOLD}
      - MAXIMUM_NUMBER_OF_MATCHES=${MAXIMUM_NUMBER_OF_MATCHES}
    networks:
      - smarthr-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  smarthr-frontend:
    build:
      context: ./smarthr-fe
      dockerfile: Dockerfile
      args:
        - VITE_BASE_API_URL=http://localhost:8080
        - VITE_MSAL_CLIENT_ID=${VITE_MSAL_CLIENT_ID:-d35b6143-5032-41ba-b03b-1dae6c2cda10}
        - VITE_MSAL_AUTHORITY_URL=${VITE_MSAL_AUTHORITY_URL:-https://login.microsoftonline.com/19eee545-4131-45c6-9a60-1a17e5cc507d}
        - VITE_MSAL_REDIRECT_URI=${VITE_MSAL_REDIRECT_URI:-http://localhost:3000}
        - VITE_LUMUSAI_API_URL=${VITE_LUMUSAI_API_URL:-https://apilumusaideveu.azurewebsites.net}
        - VITE_UPLOAD_MAX_FILES=${VITE_UPLOAD_MAX_FILES:-5}
        - VITE_UPLOAD_MAX_FILES_SIZE=${VITE_UPLOAD_MAX_FILES_SIZE:-2097152}
    container_name: smarthr-frontend
    restart: unless-stopped
    ports:
      - "5173:3000"
    depends_on:
      smarthr-backend:
        condition: service_healthy
    networks:
      - smarthr-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  smarthr-network:
    driver: bridge
