"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?m(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))c.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as d}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const u=(e,o)=>{const r=n.useContext(l),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M3 12L21 12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M16.2857 3L10.068 3C7.82129 3 6 4.82129 6 7.06797C6 8.81895 7.12044 10.3735 8.78157 10.9272L12 12M6 21H13.932C16.1787 21 18 19.1787 18 16.932C18 16.2409 17.8255 15.5804 17.512 15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=d(u);var L=C;export{L as default};
