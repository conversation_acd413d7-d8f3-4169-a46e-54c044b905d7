"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var C=(t,o,r)=>o in t?i(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))l.call(o,r)&&C(t,r,o[r]);if(s)for(var r of s(o))m.call(o,r)&&C(t,r,o[r]);return t};import*as e from"react";import{forwardRef as f}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(c),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",strokeWidth:1.5,height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M19 20H5C3.89543 20 3 19.1046 3 18V9C3 7.89543 3.89543 7 5 7H19C20.1046 7 21 7.89543 21 9V18C21 19.1046 20.1046 20 19 20Z",stroke:"currentColor"}),e.createElement("path",{d:"M16.5 14C16.2239 14 16 13.7761 16 13.5C16 13.2239 16.2239 13 16.5 13C16.7761 13 17 13.2239 17 13.5C17 13.7761 16.7761 14 16.5 14Z",fill:"currentColor",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M18 7V5.60322C18 4.28916 16.7544 3.33217 15.4847 3.67075L4.48467 6.60409C3.60917 6.83756 3 7.63046 3 8.53656V9",stroke:"currentColor"}))},d=f(u);var a=d;export{a as default};
