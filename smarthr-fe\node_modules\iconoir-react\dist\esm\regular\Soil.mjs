"use client";var u=Object.defineProperty;var s=Object.getOwnPropertySymbols;var k=Object.prototype.hasOwnProperty,L=Object.prototype.propertyIsEnumerable;var i=(e,r,n)=>r in e?u(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n,t=(e,r)=>{for(var n in r||(r={}))k.call(r,n)&&i(e,n,r[n]);if(s)for(var n of s(r))L.call(r,n)&&i(e,n,r[n]);return e};import*as o from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const l=(e,r)=>{const n=o.useContext(c),d=t(t({},n),e);return o.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:r},d),o.createElement("path",{d:"M2 4L22 4",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M3 8.01L3.01 7.99889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M3 16.01L3.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M6 12.01L6.01 11.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M6 20.01L6.01 19.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M9 8.01L9.01 7.99889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M9 16.01L9.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M12 12.01L12.01 11.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M12 20.01L12.01 19.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M15 8.01L15.01 7.99889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M15 16.01L15.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M18 12.01L18.01 11.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M18 20.01L18.01 19.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M21 8.01L21.01 7.99889",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M21 16.01L21.01 15.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},a=p(l);var m=a;export{m as default};
