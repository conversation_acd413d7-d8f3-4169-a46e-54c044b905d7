"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,t=(e,o)=>{for(var r in o||(o={}))u.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&i(e,r,o[r]);return e};import*as n from"react";import{forwardRef as L}from"react";import{IconoirContext as C}from"../IconoirContext.mjs";const c=(e,o)=>{const r=n.useContext(C),p=t(t({},r),e);return n.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M18 14C19.1046 14 20 13.1046 20 12C20 10.8954 19.1046 10 18 10C16.8954 10 16 10.8954 16 12C16 13.1046 16.8954 14 18 14Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M18 7C19.1046 7 20 6.10457 20 5C20 3.89543 19.1046 3 18 3C16.8954 3 16 3.89543 16 5C16 6.10457 16.8954 7 18 7Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M3 5L13 5",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M3 12L13 12",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M3 19L13 19",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),n.createElement("path",{d:"M16 21.2426L18.1213 19.1213M18.1213 19.1213L20.2426 17M18.1213 19.1213L16 17M18.1213 19.1213L20.2426 21.2426",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=L(c);var M=l;export{M as default};
