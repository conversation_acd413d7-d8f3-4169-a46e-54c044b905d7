"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?d(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))u.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))k.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as C}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const L=(t,o)=>{const r=e.useContext(c),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M9.6087 7H14.3913C15.832 7 17 8.16795 17 9.6087C17 9.82481 16.8248 10 16.6087 10H7.3913C7.17519 10 7 9.82481 7 9.6087C7 8.16795 8.16795 7 9.6087 7Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M9 3H15C18.3137 3 21 5.68629 21 9V13C21 16.3137 18.3137 19 15 19H9C5.68629 19 3 16.3137 3 13V9C3 5.68629 5.68629 3 9 3Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M16 15.01L16.01 14.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M8 15.01L8.01 14.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M10.5 19L8.5 21.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M13.5 19L15.5 21.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M16.5 19L18.5 21.5",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M7.5 19L5.5 21.5",stroke:"currentColor",strokeLinecap:"round"}))},l=C(L);var w=l;export{w as default};
