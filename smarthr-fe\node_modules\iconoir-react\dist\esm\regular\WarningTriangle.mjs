"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))d.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as f}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(l),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M20.0429 21H3.95705C2.41902 21 1.45658 19.3364 2.22324 18.0031L10.2662 4.01533C11.0352 2.67792 12.9648 2.67791 13.7338 4.01532L21.7768 18.0031C22.5434 19.3364 21.581 21 20.0429 21Z",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M12 9V13",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M12 17.01L12.01 16.9989",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=f(u);var L=C;export{L as default};
