"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))d.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as f}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(l),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("rect",{x:9,y:2,width:6,height:12,rx:3,stroke:"currentColor",strokeWidth:1.5}),e.createElement("path",{d:"M5 10V11C5 14.866 8.13401 18 12 18V18V18C15.866 18 19 14.866 19 11V10",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M12 18V22M12 22H9M12 22H15",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},h=f(u);var x=h;export{x as default};
