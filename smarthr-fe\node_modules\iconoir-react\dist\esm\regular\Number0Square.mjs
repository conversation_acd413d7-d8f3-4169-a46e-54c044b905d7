"use client";var C=Object.defineProperty;var s=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var m=(t,o,r)=>o in t?C(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))f.call(o,r)&&m(t,r,o[r]);if(s)for(var r of s(o))i.call(o,r)&&m(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const V=(t,o)=>{const r=n.useContext(c),p=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),n.createElement("path",{d:"M3 20.4V3.6C3 3.26863 3.26863 3 3.6 3H20.4C20.7314 3 21 3.26863 21 3.6V20.4C21 20.7314 20.7314 21 20.4 21H3.6C3.26863 21 3 20.7314 3 20.4Z",stroke:"currentColor"}),n.createElement("path",{d:"M9.5 14V10C9.5 8.89543 10.3954 8 11.5 8H12.5C13.6046 8 14.5 8.89543 14.5 10V14C14.5 15.1046 13.6046 16 12.5 16H11.5C10.3954 16 9.5 15.1046 9.5 14Z",stroke:"currentColor"}))},w=l(V);var g=w;export{g as default};
