"use client";var i=Object.defineProperty;var n=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var C=(r,e,o)=>e in r?i(r,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):r[e]=o,t=(r,e)=>{for(var o in e||(e={}))f.call(e,o)&&C(r,o,e[o]);if(n)for(var o of n(e))p.call(e,o)&&C(r,o,e[o]);return r};import*as l from"react";import{forwardRef as s}from"react";import{IconoirContext as m}from"../IconoirContext.mjs";const u=(r,e)=>{const o=l.useContext(m),d=t(t({},o),r);return l.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:e},d),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 5C2.25 2.92894 3.92894 1.25 6 1.25H18C20.0711 1.25 21.75 2.92894 21.75 5V7C21.75 9.07106 20.0711 10.75 18 10.75H6C3.92894 10.75 2.25 9.07106 2.25 7V5Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 15.25C3.41421 15.25 3.75 15.5858 3.75 16V18C3.75 19.7949 5.20508 21.25 7 21.25H17C18.7949 21.25 20.25 19.7949 20.25 18V16C20.25 15.5858 20.5858 15.25 21 15.25C21.4142 15.25 21.75 15.5858 21.75 16V18C21.75 20.6233 19.6233 22.75 17 22.75H7C4.37664 22.75 2.25 20.6233 2.25 18V16C2.25 15.5858 2.58579 15.25 3 15.25Z",fill:"currentColor"}),l.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 9.25C12.4142 9.25 12.75 9.58579 12.75 10V16.1893L14.4697 14.4697C14.7626 14.1768 15.2374 14.1768 15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303L12.5303 18.5303C12.2374 18.8232 11.7626 18.8232 11.4697 18.5303L8.46967 15.5303C8.17678 15.2374 8.17678 14.7626 8.46967 14.4697C8.76256 14.1768 9.23744 14.1768 9.53033 14.4697L11.25 16.1893V10C11.25 9.58579 11.5858 9.25 12 9.25Z",fill:"currentColor"}))},V=s(u);var R=V;export{R as default};
