"use client";var i=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var l=(r,o,e)=>o in r?i(r,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[o]=e,t=(r,o)=>{for(var e in o||(o={}))m.call(o,e)&&l(r,e,o[e]);if(s)for(var e of s(o))p.call(o,e)&&l(r,e,o[e]);return r};import*as n from"react";import{forwardRef as f}from"react";import{IconoirContext as d}from"../IconoirContext.mjs";const u=(r,o)=>{const e=n.useContext(d),c=t(t({},e),r);return n.createElement("svg",t({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},c),n.createElement("circle",{cx:12,cy:12,r:10,stroke:"currentColor",strokeWidth:1.5}),n.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9304 17.869C13.6084 18.7988 12.2931 18.798 11.9721 17.8678L10.3524 13.1739L5.78287 11.2307C4.87733 10.8456 4.96832 9.53344 5.91837 9.27705L16.1497 6.51591C16.9526 6.29922 17.6707 7.0693 17.3986 7.85518L13.9304 17.869Z",stroke:"currentColor"}))},C=f(u);var S=C;export{S as default};
