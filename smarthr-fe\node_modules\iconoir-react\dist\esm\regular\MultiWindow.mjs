"use client";var C=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var p=(t,o,r)=>o in t?C(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&p(t,r,o[r]);if(s)for(var r of s(o))d.call(o,r)&&p(t,r,o[r]);return t};import*as e from"react";import{forwardRef as l}from"react";import{IconoirContext as u}from"../IconoirContext.mjs";const c=(t,o)=>{const r=e.useContext(u),i=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},i),e.createElement("path",{d:"M7 19V11C7 9.89543 7.89543 9 9 9H20C21.1046 9 22 9.89543 22 11V19C22 20.1046 21.1046 21 20 21H9C7.89543 21 7 20.1046 7 19Z",stroke:"currentColor"}),e.createElement("path",{d:"M6.5 16H4C2.89543 16 2 15.1046 2 14V6C2 4.89543 2.89543 4 4 4H15C16.1046 4 17 4.89543 17 6V9",stroke:"currentColor"}),e.createElement("path",{d:"M10 12H11",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M5 7H6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},f=l(c);var a=f;export{a as default};
