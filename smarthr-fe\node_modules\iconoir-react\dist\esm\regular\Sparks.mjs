"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,e=(t,o)=>{for(var r in o||(o={}))C.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))f.call(o,r)&&i(t,r,o[r]);return t};import*as n from"react";import{forwardRef as l}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const r=n.useContext(c),m=e(e({},r),t);return n.createElement("svg",e({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},m),n.createElement("path",{d:"M8 15C12.8747 15 15 12.949 15 8C15 12.949 17.1104 15 22 15C17.1104 15 15 17.1104 15 22C15 17.1104 12.8747 15 8 15Z",stroke:"currentColor",strokeLinejoin:"round"}),n.createElement("path",{d:"M2 6.5C5.13376 6.5 6.5 5.18153 6.5 2C6.5 5.18153 7.85669 6.5 11 6.5C7.85669 6.5 6.5 7.85669 6.5 11C6.5 7.85669 5.13376 6.5 2 6.5Z",stroke:"currentColor",strokeLinejoin:"round"}))},u=l(d);var x=u;export{x as default};
