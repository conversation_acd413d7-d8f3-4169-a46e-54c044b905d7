"use client";var d=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;var i=(e,o,r)=>o in e?d(e,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[o]=r,n=(e,o)=>{for(var r in o||(o={}))m.call(o,r)&&i(e,r,o[r]);if(s)for(var r of s(o))u.call(o,r)&&i(e,r,o[r]);return e};import*as t from"react";import{forwardRef as c}from"react";import{IconoirContext as L}from"../IconoirContext.mjs";const f=(e,o)=>{const r=t.useContext(L),p=n(n({},r),e);return t.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),t.createElement("path",{d:"M18 21V11M18 21L16 18.5M18 21L20 18.5M18 11L16 13M18 11L20 13",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M9 5L9 17M9 17H7M9 17H11",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),t.createElement("path",{d:"M15 7V5L3 5L3 7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},l=c(f);var w=l;export{w as default};
