"use client";var p=Object.defineProperty;var s=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?p(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))m.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))l.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as C}from"react";import{IconoirContext as f}from"../IconoirContext.mjs";const d=(t,o)=>{const r=e.useContext(f),c=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",strokeWidth:1.5,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},c),e.createElement("path",{d:"M7 18V17C7 14.2386 9.23858 12 12 12V12C14.7614 12 17 14.2386 17 17V18",stroke:"currentColor",strokeLinecap:"round"}),e.createElement("path",{d:"M12 12C13.6569 12 15 10.6569 15 9C15 7.34315 13.6569 6 12 6C10.3431 6 9 7.34315 9 9C9 10.6569 10.3431 12 12 12Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("circle",{cx:12,cy:12,r:10,stroke:"currentColor",strokeWidth:1.5}))},u=C(d);var x=u;export{x as default};
