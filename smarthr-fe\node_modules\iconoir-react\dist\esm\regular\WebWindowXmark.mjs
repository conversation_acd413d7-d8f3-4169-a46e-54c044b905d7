"use client";var m=Object.defineProperty;var s=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable;var i=(t,o,r)=>o in t?m(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,n=(t,o)=>{for(var r in o||(o={}))c.call(o,r)&&i(t,r,o[r]);if(s)for(var r of s(o))d.call(o,r)&&i(t,r,o[r]);return t};import*as e from"react";import{forwardRef as f}from"react";import{IconoirContext as l}from"../IconoirContext.mjs";const u=(t,o)=>{const r=e.useContext(l),p=n(n({},r),t);return e.createElement("svg",n({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),e.createElement("path",{d:"M5 7H6",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),e.createElement("path",{d:"M2 17.7143V6.28571C2 5.02335 2.99492 4 4.22222 4H19.7778C21.0051 4 22 5.02335 22 6.28571V17.7143C22 18.9767 21.0051 20 19.7778 20H4.22222C2.99492 20 2 18.9767 2 17.7143Z",stroke:"currentColor"}),e.createElement("path",{d:"M10 14L12 12M12 12L14 10M12 12L10 10M12 12L14 14",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}))},C=f(u);var V=C;export{V as default};
