"use client";var f=Object.defineProperty;var i=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;var n=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,l=(t,o)=>{for(var e in o||(o={}))C.call(o,e)&&n(t,e,o[e]);if(i)for(var e of i(o))s.call(o,e)&&n(t,e,o[e]);return t};import*as r from"react";import{forwardRef as m}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=r.useContext(c),p=l(l({},e),t);return r.createElement("svg",l({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},p),r.createElement("g",{clipPath:"url(#clip0_3839_8176)"},r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.0454 0.893453C11.5726 0.366246 12.4274 0.366244 12.9546 0.893452L23.1061 11.0449C23.6333 11.5721 23.6333 12.4269 23.1061 12.9541L12.9546 23.1056C12.4274 23.6328 11.5726 23.6328 11.0454 23.1056L0.893941 12.9541C0.366734 12.4269 0.366732 11.5721 0.893941 11.0449L11.0454 0.893453ZM5.25 12C5.25 11.5858 5.58579 11.25 6 11.25H10C10.4142 11.25 10.75 11.5858 10.75 12C10.75 12.4142 10.4142 12.75 10 12.75H6C5.58579 12.75 5.25 12.4142 5.25 12ZM14 11.25C13.5858 11.25 13.25 11.5858 13.25 12C13.25 12.4142 13.5858 12.75 14 12.75H18C18.4142 12.75 18.75 12.4142 18.75 12C18.75 11.5858 18.4142 11.25 18 11.25H14Z",fill:"currentColor"})),r.createElement("defs",null,r.createElement("clipPath",{id:"clip0_3839_8176"},r.createElement("rect",{width:24,height:24,fill:"white"}))))},h=m(d);var S=h;export{S as default};
