"use client";var f=Object.defineProperty;var n=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var l=(t,o,e)=>o in t?f(t,o,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[o]=e,r=(t,o)=>{for(var e in o||(o={}))i.call(o,e)&&l(t,e,o[e]);if(n)for(var e of n(o))m.call(o,e)&&l(t,e,o[e]);return t};import*as C from"react";import{forwardRef as p}from"react";import{IconoirContext as c}from"../IconoirContext.mjs";const d=(t,o)=>{const e=C.useContext(c),s=r(r({},e),t);return C.createElement("svg",r({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:o},s),C.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.25 3.6C2.25 2.85442 2.85442 2.25 3.6 2.25H20.4C21.1456 2.25 21.75 2.85444 21.75 3.6V20.4C21.75 21.1456 21.1456 21.75 20.4 21.75H3.6C2.85444 21.75 2.25 21.1456 2.25 20.4V3.6ZM15.5793 7.52029C15.564 7.50344 15.5478 7.48728 15.531 7.47189C15.4667 7.41301 15.3946 7.36736 15.3183 7.33495C15.2282 7.29661 15.129 7.27539 15.0249 7.27539H15.0246H10.0752C9.66098 7.27539 9.3252 7.61118 9.3252 8.02539C9.3252 8.4396 9.66098 8.77539 10.0752 8.77539H13.2142L10.9591 11.0306C10.2475 11.7421 9.85804 12.4904 9.70986 13.2313C9.56278 13.9667 9.66365 14.6426 9.84979 15.201C10.0349 15.7563 10.3088 16.21 10.5311 16.5212C10.7276 16.7964 10.8911 16.9711 10.9435 17.0252C11.2326 17.32 11.7233 17.3374 12.0197 17.041C12.3124 16.7483 12.313 16.2742 12.0206 15.9813C11.9228 15.8778 11.8344 15.7652 11.7517 15.6494C11.5872 15.4192 11.3971 15.0995 11.2728 14.7267C11.1496 14.357 11.0958 13.9502 11.1807 13.5255C11.2646 13.1063 11.4938 12.6172 12.0197 12.0913L14.2749 9.83606V12.9752C14.2749 13.3894 14.6107 13.7252 15.0249 13.7252C15.4391 13.7252 15.7749 13.3894 15.7749 12.9752V8.02539C15.7749 7.91803 15.7523 7.81593 15.7117 7.72359C15.6797 7.65075 15.6356 7.58196 15.5793 7.52029Z",fill:"currentColor"}))},V=p(d);var g=V;export{g as default};
