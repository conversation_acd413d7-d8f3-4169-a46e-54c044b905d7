"use client";var s=Object.defineProperty;var C=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var n=(e,L,o)=>L in e?s(e,L,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[L]=o,t=(e,L)=>{for(var o in L||(L={}))f.call(L,o)&&n(e,o,L[o]);if(C)for(var o of C(L))i.call(L,o)&&n(e,o,L[o]);return e};import*as r from"react";import{forwardRef as m}from"react";import{IconoirContext as p}from"../IconoirContext.mjs";const c=(e,L)=>{const o=r.useContext(p),l=t(t({},o),e);return r.createElement("svg",t({width:"1.5em",height:"1.5em",viewBox:"0 0 24 24",strokeWidth:1.5,fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",ref:L},l),r.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6553 3.15717C17.9372 4.43908 18.75 6.32533 18.75 8.75004V9.50003L19.6277 9.50002C20.941 9.50001 21.4811 11.1852 20.4124 11.9486L18.8967 13.0312C19.3773 14.1619 20.5856 15.923 22.706 16.5289C22.9363 16.5947 23.1213 16.7665 23.204 16.9912C23.2866 17.216 23.2569 17.4668 23.124 17.666C22.911 17.9855 22.5247 18.4273 22.0137 18.7958C21.5937 19.0987 21.0509 19.3793 20.4216 19.4697L19.774 20.4411L19.7377 20.4956L19.7336 20.5017L19.7112 20.5353L19.7034 20.547L19.693 20.5625L19.6921 20.564L19.6911 20.5655L19.6901 20.5669L19.6836 20.5766L19.6828 20.578L19.6819 20.5793L19.6801 20.5819L19.6793 20.5832L19.6776 20.5857L19.6768 20.5869L19.676 20.5882L19.6751 20.5894L19.6743 20.5906L19.6735 20.5918L19.6728 20.5929L19.672 20.5941L19.6705 20.5964L19.6668 20.6019L19.6661 20.603L19.6647 20.6051L19.662 20.6092L19.6613 20.6101L19.6594 20.613L19.6587 20.614L19.6569 20.6167L19.6563 20.6176L19.6557 20.6185L19.6551 20.6194L19.654 20.6211L19.6534 20.6219L19.6523 20.6236L19.6502 20.6268L19.6492 20.6283L19.6482 20.6298L19.6472 20.6312L19.6463 20.6327L19.6454 20.634L19.6445 20.6354L19.6436 20.6367L19.6428 20.6379L19.642 20.6391L19.6412 20.6403L19.6404 20.6415L19.6397 20.6426L19.6389 20.6437L19.6382 20.6447L19.6376 20.6457L19.6369 20.6467L19.6363 20.6477L19.6357 20.6486L19.6351 20.6495L19.6345 20.6503L19.6337 20.6516L19.6329 20.6527L19.6322 20.6538L19.6315 20.6549L19.6308 20.6558L19.6302 20.6567L19.6297 20.6576L19.6289 20.6587L19.6283 20.6596L19.6277 20.6605L19.6271 20.6615L19.6265 20.6624L19.6259 20.6632L19.6253 20.6642L19.6247 20.665L19.6242 20.6658C19.4556 20.9187 19.1509 21.045 18.8529 20.9854L16.6863 20.5521C16.2282 20.8905 15.5868 21.3084 14.877 21.6688C14.0233 22.1021 12.9787 22.5 12 22.5C11.0213 22.5 9.97666 22.1021 9.12303 21.6688C8.41319 21.3084 7.77179 20.8905 7.31369 20.5521L5.14709 20.9854C4.84907 21.045 4.54455 20.9189 4.37597 20.666L3.57842 19.4697C2.94907 19.3793 2.40627 19.0987 1.98631 18.7958C1.47533 18.4273 1.08898 17.9855 0.875966 17.666C0.743121 17.4668 0.713428 17.216 0.796054 16.9912C0.878679 16.7665 1.06369 16.5947 1.29396 16.5289C3.4144 15.923 4.62269 14.1619 5.10327 13.0312L3.58764 11.9486C2.51893 11.1852 3.05898 9.50001 4.37232 9.50002L5.25 9.50003V8.75004C5.25 6.32534 6.06277 4.4391 7.34467 3.15719C8.61952 1.88233 10.3145 1.25 12 1.25C13.6855 1.25 15.3805 1.88232 16.6553 3.15717Z",fill:"currentColor"}))},d=m(c);var S=d;export{S as default};
